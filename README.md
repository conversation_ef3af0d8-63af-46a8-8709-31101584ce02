BMI270 Sensor API

> This package contains the sensor APIs for the BMI270 sensor

## Sensor Overview
The BMI270 is a small, low power, low noise inertial measurement unit designed for use in mobile applications like augmented reality or indoor navigation which require highly accurate, real-time sensor data.

## Applications

### BMI270 (base)

- Any motion, No motion, Significant motion detectors
- Wrist worn Step counter and Step detector (Pedometer)
- Activity change recognition
  - Still
  - Walking
  - Running
- Wrist gestures
  - Push arm down
  - Pivot up
  - Wrist shake jiggle
  - Flick in
  - Flick out
- Wrist wear wake up

### BMI270 Context

- Step counter and Step detector (Pedometer)
- Activity change recognition
  - Still
  - Walking
  - Running

### BMI270 Legacy

- Any motion, No motion, Significant motion detector
- Orientation detector (Advanced Potrait-Landscape)
- High-G, Low-G (Freefall) detector
- Flat detector
- Tap detection (Single, Double, Triple taps)
- Smartphone Step counter and Step detector (Pedometer)
- Activity change recognition
  - Still
  - Walking
  - Running

### BMI270 Maximum FIFO

- Supports a 6kB FIFO

For more information refer product page [Link](https://www.bosch-sensortec.com/products/motion-sensors/imus/bmi270.html) 

---