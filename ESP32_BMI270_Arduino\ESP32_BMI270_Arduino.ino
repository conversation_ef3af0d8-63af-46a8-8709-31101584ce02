/**
 * ESP32-C3 BMI270 Sensor Data Reader
 *
 * This Arduino sketch reads accelerometer and gyroscope data from BMI270 sensor
 * using ESP32-C3 microcontroller via I2C interface.
 *
 * Hardware Connections (I2C):
 *   - SDA: GPIO6 (ESP32-C3)
 *   - SCL: GPIO7 (ESP32-C3)
 *   - VCC: 3.3V
 *   - GND: GND
 */

#include <Wire.h>

// BMI270 I2C Address
#define BMI270_I2C_ADDR_PRIMARY   0x68
#define BMI270_I2C_ADDR_SECONDARY 0x69

// BMI270 Register Addresses
#define BMI270_CHIP_ID_REG        0x00
#define BMI270_STATUS_REG         0x03
#define BMI270_ACC_DATA_X_LSB     0x0C
#define BMI270_ACC_DATA_X_MSB     0x0D
#define BMI270_ACC_DATA_Y_LSB     0x0E
#define BMI270_ACC_DATA_Y_MSB     0x0F
#define BMI270_ACC_DATA_Z_LSB     0x10
#define BMI270_ACC_DATA_Z_MSB     0x11
#define BMI270_GYR_DATA_X_LSB     0x12
#define BMI270_GYR_DATA_X_MSB     0x13
#define BMI270_GYR_DATA_Y_LSB     0x14
#define BMI270_GYR_DATA_Y_MSB     0x15
#define BMI270_GYR_DATA_Z_LSB     0x16
#define BMI270_GYR_DATA_Z_MSB     0x17
#define BMI270_TEMP_DATA_LSB      0x22
#define BMI270_TEMP_DATA_MSB      0x23

// Configuration Registers
#define BMI270_ACC_CONF_REG       0x40
#define BMI270_ACC_RANGE_REG      0x41
#define BMI270_GYR_CONF_REG       0x42
#define BMI270_GYR_RANGE_REG      0x43
#define BMI270_PWR_CONF_REG       0x7C
#define BMI270_PWR_CTRL_REG       0x7D
#define BMI270_INIT_CTRL_REG      0x59
#define BMI270_INIT_DATA_REG      0x5E

// BMI270 Constants
#define BMI270_CHIP_ID            0x24
#define BMI270_POWER_ON_TIME      450  // microseconds
#define BMI270_SOFT_RESET_TIME    2000 // microseconds

// Configuration values
#define BMI270_ACC_ODR_200HZ      0x0B
#define BMI270_ACC_RANGE_2G       0x00
#define BMI270_ACC_RANGE_4G       0x01
#define BMI270_ACC_RANGE_8G       0x02
#define BMI270_ACC_RANGE_16G      0x03

#define BMI270_GYR_ODR_200HZ      0x0B
#define BMI270_GYR_RANGE_2000DPS  0x00
#define BMI270_GYR_RANGE_1000DPS  0x01
#define BMI270_GYR_RANGE_500DPS   0x02
#define BMI270_GYR_RANGE_250DPS   0x03
#define BMI270_GYR_RANGE_125DPS   0x04

// Pin definitions for ESP32-C3 I2C
#define SDA_PIN    6
#define SCL_PIN    7

// Global variables
uint8_t bmi270_i2c_addr = BMI270_I2C_ADDR_PRIMARY;

// Sensor data structure
struct BMI270_Data {
  int16_t acc_x, acc_y, acc_z;
  int16_t gyr_x, gyr_y, gyr_z;
  int16_t temperature;
  float acc_x_ms2, acc_y_ms2, acc_z_ms2;
  float gyr_x_dps, gyr_y_dps, gyr_z_dps;
  float temp_celsius;
};

BMI270_Data sensor_data;

void setup() {
  Serial.begin(115200);
  delay(2000); // Give time for serial to initialize

  Serial.println("\n\n=== BMI270 Sensor Test with ESP32-C3 (I2C) ===");
  Serial.println("Starting fresh initialization...");
  Serial.flush();

  // Initialize I2C interface
  Wire.begin(SDA_PIN, SCL_PIN);
  Wire.setClock(100000); // Start with 100kHz I2C for better reliability
  Serial.println("I2C interface initialized at 100kHz");
  Serial.flush();

  // Scan for I2C devices
  Serial.println("Scanning I2C bus...");
  Serial.flush();
  scanI2C();

  // Simple BMI270 test
  Serial.println("Testing BMI270 communication...");
  Serial.flush();

  // Try both addresses
  bool found = false;
  uint8_t addresses[] = {BMI270_I2C_ADDR_PRIMARY, BMI270_I2C_ADDR_SECONDARY};

  for (int i = 0; i < 2; i++) {
    bmi270_i2c_addr = addresses[i];
    Serial.print("Testing address 0x");
    Serial.println(bmi270_i2c_addr, HEX);

    uint8_t chip_id = readRegister(BMI270_CHIP_ID_REG);
    Serial.print("Chip ID: 0x");
    Serial.println(chip_id, HEX);

    if (chip_id == BMI270_CHIP_ID) {
      Serial.println("BMI270 found!");
      found = true;
      break;
    }
  }

  if (!found) {
    Serial.println("BMI270 not found!");
    while(1) delay(1000);
  }

  // Simple initialization
  Serial.println("Starting simple initialization...");
  simpleInit();

  Serial.println("Setup complete. Starting data reading...");
  Serial.println("===========================================");
  Serial.flush();
}

void loop() {
  static int loop_count = 0;

  // Periodic diagnostics
  if (loop_count % 20 == 0) {
    Serial.println("\n--- Diagnostic Check ---");
    uint8_t chip_id = readRegister(BMI270_CHIP_ID_REG);
    uint8_t status = readRegister(BMI270_STATUS_REG);
    uint8_t pwr_ctrl = readRegister(BMI270_PWR_CTRL_REG);

    Serial.print("Chip ID: 0x"); Serial.println(chip_id, HEX);
    Serial.print("Status: 0x"); Serial.println(status, HEX);
    Serial.print("Power Control: 0x"); Serial.println(pwr_ctrl, HEX);
    Serial.println("--- End Diagnostic ---\n");
  }
  loop_count++;

  if (readBMI270Data(&sensor_data)) {
    // Convert raw data to physical units
    convertAccelData(&sensor_data);
    convertGyroData(&sensor_data);
    convertTempData(&sensor_data);

    // Print raw data for debugging (first few readings)
    static int debug_count = 0;
    if (debug_count < 10) {
      Serial.print("Raw - Acc: ");
      Serial.print(sensor_data.acc_x); Serial.print(", ");
      Serial.print(sensor_data.acc_y); Serial.print(", ");
      Serial.print(sensor_data.acc_z);
      Serial.print(" | Gyr: ");
      Serial.print(sensor_data.gyr_x); Serial.print(", ");
      Serial.print(sensor_data.gyr_y); Serial.print(", ");
      Serial.print(sensor_data.gyr_z);
      Serial.print(" | Temp: ");
      Serial.println(sensor_data.temperature);
      debug_count++;
    }

    // Print converted data
    Serial.print("Accel: ");
    Serial.print(sensor_data.acc_x_ms2, 2); Serial.print(", ");
    Serial.print(sensor_data.acc_y_ms2, 2); Serial.print(", ");
    Serial.print(sensor_data.acc_z_ms2, 2);

    Serial.print(" | Gyro: ");
    Serial.print(sensor_data.gyr_x_dps, 2); Serial.print(", ");
    Serial.print(sensor_data.gyr_y_dps, 2); Serial.print(", ");
    Serial.print(sensor_data.gyr_z_dps, 2);

    Serial.print(" | Temp: ");
    Serial.print(sensor_data.temp_celsius, 1);
    Serial.println("°C");
  } else {
    Serial.println("Failed to read sensor data");
  }

  delay(1000); // 1Hz for easier debugging
}

void scanI2C() {
  byte error, address;
  int nDevices = 0;

  Serial.println("Scanning for I2C devices...");
  for(address = 1; address < 127; address++) {
    Wire.beginTransmission(address);
    error = Wire.endTransmission();

    if (error == 0) {
      Serial.print("I2C device found at address 0x");
      if (address < 16) Serial.print("0");
      Serial.println(address, HEX);
      nDevices++;

      // Check if this might be BMI270
      if (address == BMI270_I2C_ADDR_PRIMARY || address == BMI270_I2C_ADDR_SECONDARY) {
        bmi270_i2c_addr = address;
        Serial.print("Using BMI270 I2C address: 0x");
        Serial.println(address, HEX);
      }
    }
  }

  if (nDevices == 0) {
    Serial.println("No I2C devices found");
  } else {
    Serial.print("Found ");
    Serial.print(nDevices);
    Serial.println(" I2C device(s)");
  }
  Serial.println();
}

void simpleInit() {
  Serial.println("Step 1: Soft reset");
  writeRegister(0x7E, 0xB6); // Soft reset
  delay(300);

  Serial.println("Step 2: Check chip ID after reset");
  uint8_t chip_id = readRegister(BMI270_CHIP_ID_REG);
  Serial.print("Chip ID after reset: 0x");
  Serial.println(chip_id, HEX);

  Serial.println("Step 3: Disable advanced power save");
  writeRegister(BMI270_PWR_CONF_REG, 0x00);
  delay(10);

  Serial.println("Step 4: Enable accelerometer and gyroscope");
  writeRegister(BMI270_PWR_CTRL_REG, 0x0E);
  delay(100);

  Serial.println("Step 5: Basic accelerometer config");
  writeRegister(BMI270_ACC_CONF_REG, 0x28); // Simple config
  writeRegister(BMI270_ACC_RANGE_REG, 0x00); // 2G range
  delay(50);

  Serial.println("Step 6: Basic gyroscope config");
  writeRegister(BMI270_GYR_CONF_REG, 0x29); // Simple config
  writeRegister(BMI270_GYR_RANGE_REG, 0x00); // 2000 DPS
  delay(100);

  // Check final status
  uint8_t pwr_ctrl = readRegister(BMI270_PWR_CTRL_REG);
  uint8_t status = readRegister(BMI270_STATUS_REG);

  Serial.print("Final power control: 0x");
  Serial.println(pwr_ctrl, HEX);
  Serial.print("Final status: 0x");
  Serial.println(status, HEX);

  Serial.println("Simple initialization complete");
}

bool initBMI270() {
  Serial.println("Initializing BMI270...");

  // Try both possible I2C addresses
  bool found = false;
  uint8_t addresses[] = {BMI270_I2C_ADDR_PRIMARY, BMI270_I2C_ADDR_SECONDARY};

  for (int i = 0; i < 2; i++) {
    bmi270_i2c_addr = addresses[i];
    Serial.print("Trying I2C address: 0x");
    Serial.println(bmi270_i2c_addr, HEX);

    // Check chip ID
    uint8_t chip_id = readRegister(BMI270_CHIP_ID_REG);
    Serial.print("Read chip ID: 0x");
    Serial.println(chip_id, HEX);

    if (chip_id == BMI270_CHIP_ID) {
      Serial.println("BMI270 found!");
      found = true;
      break;
    }
  }

  if (!found) {
    Serial.println("BMI270 not found on either address");
    return false;
  }

  // Soft reset - use CMD register
  Serial.println("Performing soft reset...");
  writeRegister(0x7E, 0xB6); // CMD register soft reset
  delay(200); // Wait longer for reset

  // Check if reset was successful
  uint8_t chip_id_after_reset = readRegister(BMI270_CHIP_ID_REG);
  Serial.print("Chip ID after reset: 0x");
  Serial.println(chip_id_after_reset, HEX);

  // BMI270 requires initialization sequence
  Serial.println("Starting BMI270 initialization sequence...");

  // Step 1: Disable advanced power save
  writeRegister(BMI270_PWR_CONF_REG, 0x00);
  delay(1);

  // Step 2: Initialize BMI270 (simplified version)
  writeRegister(BMI270_INIT_CTRL_REG, 0x00); // Disable init
  delay(1);

  // Step 3: Enable accelerometer and gyroscope
  Serial.println("Enabling sensors...");
  writeRegister(BMI270_PWR_CTRL_REG, 0x0E); // Enable ACC and GYR
  delay(50);

  // Step 4: Configure accelerometer
  Serial.println("Configuring accelerometer...");
  writeRegister(BMI270_ACC_CONF_REG, 0xA8); // ODR=100Hz, BWP=normal, filter_perf=0
  writeRegister(BMI270_ACC_RANGE_REG, BMI270_ACC_RANGE_2G);
  delay(10);

  // Step 5: Configure gyroscope
  Serial.println("Configuring gyroscope...");
  writeRegister(BMI270_GYR_CONF_REG, 0xA9); // ODR=200Hz, BWP=normal, filter_perf=0
  writeRegister(BMI270_GYR_RANGE_REG, BMI270_GYR_RANGE_2000DPS);
  delay(50);

  // Verify configuration
  uint8_t pwr_ctrl = readRegister(BMI270_PWR_CTRL_REG);
  uint8_t acc_conf = readRegister(BMI270_ACC_CONF_REG);
  uint8_t gyr_conf = readRegister(BMI270_GYR_CONF_REG);

  Serial.print("Power control register: 0x");
  Serial.println(pwr_ctrl, HEX);
  Serial.print("Accelerometer config: 0x");
  Serial.println(acc_conf, HEX);
  Serial.print("Gyroscope config: 0x");
  Serial.println(gyr_conf, HEX);

  Serial.println("BMI270 initialization complete");
  return true;
}

bool readBMI270Data(BMI270_Data* data) {
  // Check status register to see if data is ready
  uint8_t status = readRegister(BMI270_STATUS_REG);
  static int status_debug_count = 0;
  if (status_debug_count < 5) {
    Serial.print("Status register: 0x");
    Serial.println(status, HEX);
    status_debug_count++;
  }

  uint8_t buffer[12];

  // Read accelerometer and gyroscope data (12 bytes starting from ACC_DATA_X_LSB)
  if (!readMultipleRegisters(BMI270_ACC_DATA_X_LSB, buffer, 12)) {
    return false;
  }

  // Parse accelerometer data
  data->acc_x = (int16_t)((buffer[1] << 8) | buffer[0]);
  data->acc_y = (int16_t)((buffer[3] << 8) | buffer[2]);
  data->acc_z = (int16_t)((buffer[5] << 8) | buffer[4]);

  // Parse gyroscope data
  data->gyr_x = (int16_t)((buffer[7] << 8) | buffer[6]);
  data->gyr_y = (int16_t)((buffer[9] << 8) | buffer[8]);
  data->gyr_z = (int16_t)((buffer[11] << 8) | buffer[10]);

  // Read temperature data
  uint8_t temp_buffer[2];
  if (readMultipleRegisters(BMI270_TEMP_DATA_LSB, temp_buffer, 2)) {
    data->temperature = (int16_t)((temp_buffer[1] << 8) | temp_buffer[0]);
  }

  return true;
}

void convertAccelData(BMI270_Data* data) {
  // Convert to m/s² (2G range, 16-bit resolution)
  float scale = (2.0 * 9.80665) / 32768.0; // 2G range
  data->acc_x_ms2 = data->acc_x * scale;
  data->acc_y_ms2 = data->acc_y * scale;
  data->acc_z_ms2 = data->acc_z * scale;
}

void convertGyroData(BMI270_Data* data) {
  // Convert to degrees per second (2000 DPS range, 16-bit resolution)
  float scale = 2000.0 / 32768.0; // 2000 DPS range
  data->gyr_x_dps = data->gyr_x * scale;
  data->gyr_y_dps = data->gyr_y * scale;
  data->gyr_z_dps = data->gyr_z * scale;
}

void convertTempData(BMI270_Data* data) {
  // Convert to Celsius (BMI270 temperature formula)
  data->temp_celsius = (data->temperature / 512.0) + 23.0;
}

uint8_t readRegister(uint8_t reg) {
  Wire.beginTransmission(bmi270_i2c_addr);
  Wire.write(reg);
  uint8_t error = Wire.endTransmission(false);

  if (error != 0) {
    Serial.print("I2C write error: ");
    Serial.println(error);
    return 0;
  }

  Wire.requestFrom(bmi270_i2c_addr, (uint8_t)1);
  if (Wire.available()) {
    return Wire.read();
  } else {
    Serial.println("No data available from I2C");
    return 0;
  }
}

void writeRegister(uint8_t reg, uint8_t data) {
  Wire.beginTransmission(bmi270_i2c_addr);
  Wire.write(reg);
  Wire.write(data);
  uint8_t error = Wire.endTransmission();

  if (error != 0) {
    Serial.print("I2C write error to reg 0x");
    Serial.print(reg, HEX);
    Serial.print(": ");
    Serial.println(error);
  }
}

bool readMultipleRegisters(uint8_t reg, uint8_t* buffer, uint8_t length) {
  Wire.beginTransmission(bmi270_i2c_addr);
  Wire.write(reg);
  uint8_t error = Wire.endTransmission(false);

  if (error != 0) {
    Serial.print("I2C write error in readMultiple: ");
    Serial.println(error);
    return false;
  }

  uint8_t received = Wire.requestFrom(bmi270_i2c_addr, length);
  if (received != length) {
    Serial.print("Expected ");
    Serial.print(length);
    Serial.print(" bytes, got ");
    Serial.println(received);
    return false;
  }

  for (uint8_t i = 0; i < length; i++) {
    if (Wire.available()) {
      buffer[i] = Wire.read();
    } else {
      Serial.print("No data at byte ");
      Serial.println(i);
      return false;
    }
  }
  return true;
}
