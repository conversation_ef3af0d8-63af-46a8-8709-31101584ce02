/**
 * ESP32-C3 BMI270 Sensor Data Reader
 *
 * This Arduino sketch reads accelerometer and gyroscope data from BMI270 sensor
 * using ESP32-C3 microcontroller via I2C interface.
 *
 * Hardware Connections (I2C):
 *   - SDA: GPIO8 (ESP32-C3)
 *   - SCL: GPIO9 (ESP32-C3)
 *   - VCC: 3.3V
 *   - GND: GND
 */

#include <Wire.h>

// BMI270 I2C Address
#define BMI270_I2C_ADDR_PRIMARY   0x68
#define BMI270_I2C_ADDR_SECONDARY 0x69

// BMI270 Register Addresses
#define BMI270_CHIP_ID_REG        0x00
#define BMI270_STATUS_REG         0x03
#define BMI270_ACC_DATA_X_LSB     0x0C
#define BMI270_ACC_DATA_X_MSB     0x0D
#define BMI270_ACC_DATA_Y_LSB     0x0E
#define BMI270_ACC_DATA_Y_MSB     0x0F
#define BMI270_ACC_DATA_Z_LSB     0x10
#define BMI270_ACC_DATA_Z_MSB     0x11
#define BMI270_GYR_DATA_X_LSB     0x12
#define BMI270_GYR_DATA_X_MSB     0x13
#define BMI270_GYR_DATA_Y_LSB     0x14
#define BMI270_GYR_DATA_Y_MSB     0x15
#define BMI270_GYR_DATA_Z_LSB     0x16
#define BMI270_GYR_DATA_Z_MSB     0x17
#define BMI270_TEMP_DATA_LSB      0x22
#define BMI270_TEMP_DATA_MSB      0x23

// Configuration Registers
#define BMI270_ACC_CONF_REG       0x40
#define BMI270_ACC_RANGE_REG      0x41
#define BMI270_GYR_CONF_REG       0x42
#define BMI270_GYR_RANGE_REG      0x43
#define BMI270_PWR_CONF_REG       0x7C
#define BMI270_PWR_CTRL_REG       0x7D
#define BMI270_INIT_CTRL_REG      0x59
#define BMI270_INIT_DATA_REG      0x5E

// BMI270 Constants
#define BMI270_CHIP_ID            0x24
#define BMI270_POWER_ON_TIME      450  // microseconds
#define BMI270_SOFT_RESET_TIME    2000 // microseconds

// Configuration values
#define BMI270_ACC_ODR_200HZ      0x0B
#define BMI270_ACC_RANGE_2G       0x00
#define BMI270_ACC_RANGE_4G       0x01
#define BMI270_ACC_RANGE_8G       0x02
#define BMI270_ACC_RANGE_16G      0x03

#define BMI270_GYR_ODR_200HZ      0x0B
#define BMI270_GYR_RANGE_2000DPS  0x00
#define BMI270_GYR_RANGE_1000DPS  0x01
#define BMI270_GYR_RANGE_500DPS   0x02
#define BMI270_GYR_RANGE_250DPS   0x03
#define BMI270_GYR_RANGE_125DPS   0x04

// Pin definitions for ESP32-C3 I2C
#define SDA_PIN    6
#define SCL_PIN    7

// Global variables
uint8_t bmi270_i2c_addr = BMI270_I2C_ADDR_PRIMARY;

// Sensor data structure
struct BMI270_Data {
  int16_t acc_x, acc_y, acc_z;
  int16_t gyr_x, gyr_y, gyr_z;
  int16_t temperature;
  float acc_x_ms2, acc_y_ms2, acc_z_ms2;
  float gyr_x_dps, gyr_y_dps, gyr_z_dps;
  float temp_celsius;
};

BMI270_Data sensor_data;

void setup() {
  Serial.begin(115200);
  while (!Serial) {
    delay(10);
  }
  
  Serial.println("BMI270 Sensor Test with ESP32-C3 (I2C)");
  Serial.println("======================================");

  // Initialize I2C interface
  Wire.begin(SDA_PIN, SCL_PIN);
  Wire.setClock(400000); // 400kHz I2C
  Serial.println("I2C interface initialized");
  
  // Initialize BMI270
  if (initBMI270()) {
    Serial.println("BMI270 initialization successful!");
  } else {
    Serial.println("BMI270 initialization failed!");
    while (1) {
      delay(1000);
    }
  }
  
  Serial.println("\nData format: Acc_X, Acc_Y, Acc_Z (m/s²), Gyr_X, Gyr_Y, Gyr_Z (°/s), Temp (°C)");
  Serial.println("=============================================================================");
}

void loop() {
  if (readBMI270Data(&sensor_data)) {
    // Convert raw data to physical units
    convertAccelData(&sensor_data);
    convertGyroData(&sensor_data);
    convertTempData(&sensor_data);
    
    // Print data
    Serial.print("Accel: ");
    Serial.print(sensor_data.acc_x_ms2, 2); Serial.print(", ");
    Serial.print(sensor_data.acc_y_ms2, 2); Serial.print(", ");
    Serial.print(sensor_data.acc_z_ms2, 2);
    
    Serial.print(" | Gyro: ");
    Serial.print(sensor_data.gyr_x_dps, 2); Serial.print(", ");
    Serial.print(sensor_data.gyr_y_dps, 2); Serial.print(", ");
    Serial.print(sensor_data.gyr_z_dps, 2);
    
    Serial.print(" | Temp: ");
    Serial.print(sensor_data.temp_celsius, 1);
    Serial.println("°C");
  } else {
    Serial.println("Failed to read sensor data");
  }
  
  delay(100); // 10Hz output rate
}

bool initBMI270() {
  // Check chip ID
  uint8_t chip_id = readRegister(BMI270_CHIP_ID_REG);
  if (chip_id != BMI270_CHIP_ID) {
    Serial.print("Wrong chip ID: 0x");
    Serial.println(chip_id, HEX);
    return false;
  }
  
  Serial.print("BMI270 Chip ID: 0x");
  Serial.println(chip_id, HEX);
  
  // Soft reset
  writeRegister(BMI270_PWR_CONF_REG, 0x00);
  delayMicroseconds(BMI270_SOFT_RESET_TIME);
  
  // Power up accelerometer and gyroscope
  writeRegister(BMI270_PWR_CTRL_REG, 0x0E); // Enable ACC and GYR
  delay(10);
  
  // Configure accelerometer
  writeRegister(BMI270_ACC_CONF_REG, BMI270_ACC_ODR_200HZ | 0x80); // 200Hz, normal mode
  writeRegister(BMI270_ACC_RANGE_REG, BMI270_ACC_RANGE_2G);
  
  // Configure gyroscope  
  writeRegister(BMI270_GYR_CONF_REG, BMI270_GYR_ODR_200HZ | 0x80); // 200Hz, normal mode
  writeRegister(BMI270_GYR_RANGE_REG, BMI270_GYR_RANGE_2000DPS);
  
  delay(50); // Wait for sensor to stabilize
  
  return true;
}

bool readBMI270Data(BMI270_Data* data) {
  uint8_t buffer[12];
  
  // Read accelerometer and gyroscope data (12 bytes starting from ACC_DATA_X_LSB)
  if (!readMultipleRegisters(BMI270_ACC_DATA_X_LSB, buffer, 12)) {
    return false;
  }
  
  // Parse accelerometer data
  data->acc_x = (int16_t)((buffer[1] << 8) | buffer[0]);
  data->acc_y = (int16_t)((buffer[3] << 8) | buffer[2]);
  data->acc_z = (int16_t)((buffer[5] << 8) | buffer[4]);
  
  // Parse gyroscope data
  data->gyr_x = (int16_t)((buffer[7] << 8) | buffer[6]);
  data->gyr_y = (int16_t)((buffer[9] << 8) | buffer[8]);
  data->gyr_z = (int16_t)((buffer[11] << 8) | buffer[10]);
  
  // Read temperature data
  uint8_t temp_buffer[2];
  if (readMultipleRegisters(BMI270_TEMP_DATA_LSB, temp_buffer, 2)) {
    data->temperature = (int16_t)((temp_buffer[1] << 8) | temp_buffer[0]);
  }
  
  return true;
}

void convertAccelData(BMI270_Data* data) {
  // Convert to m/s² (2G range, 16-bit resolution)
  float scale = (2.0 * 9.80665) / 32768.0; // 2G range
  data->acc_x_ms2 = data->acc_x * scale;
  data->acc_y_ms2 = data->acc_y * scale;
  data->acc_z_ms2 = data->acc_z * scale;
}

void convertGyroData(BMI270_Data* data) {
  // Convert to degrees per second (2000 DPS range, 16-bit resolution)
  float scale = 2000.0 / 32768.0; // 2000 DPS range
  data->gyr_x_dps = data->gyr_x * scale;
  data->gyr_y_dps = data->gyr_y * scale;
  data->gyr_z_dps = data->gyr_z * scale;
}

void convertTempData(BMI270_Data* data) {
  // Convert to Celsius (BMI270 temperature formula)
  data->temp_celsius = (data->temperature / 512.0) + 23.0;
}

uint8_t readRegister(uint8_t reg) {
  Wire.beginTransmission(bmi270_i2c_addr);
  Wire.write(reg);
  Wire.endTransmission(false);
  Wire.requestFrom(bmi270_i2c_addr, (uint8_t)1);
  return Wire.read();
}

void writeRegister(uint8_t reg, uint8_t data) {
  Wire.beginTransmission(bmi270_i2c_addr);
  Wire.write(reg);
  Wire.write(data);
  Wire.endTransmission();
}

bool readMultipleRegisters(uint8_t reg, uint8_t* buffer, uint8_t length) {
  Wire.beginTransmission(bmi270_i2c_addr);
  Wire.write(reg);
  Wire.endTransmission(false);
  Wire.requestFrom(bmi270_i2c_addr, length);

  for (uint8_t i = 0; i < length; i++) {
    if (Wire.available()) {
      buffer[i] = Wire.read();
    } else {
      return false;
    }
  }
  return true;
}
