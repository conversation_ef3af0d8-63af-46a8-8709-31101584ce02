/**
* Copyright (c) 2025 Bosch Sensortec GmbH. All rights reserved.
*
* BSD-3-Clause
*
* Redistribution and use in source and binary forms, with or without
* modification, are permitted provided that the following conditions are met:
*
* 1. Redistributions of source code must retain the above copyright
*    notice, this list of conditions and the following disclaimer.
*
* 2. Redistributions in binary form must reproduce the above copyright
*    notice, this list of conditions and the following disclaimer in the
*    documentation and/or other materials provided with the distribution.
*
* 3. Neither the name of the copyright holder nor the names of its
*    contributors may be used to endorse or promote products derived from
*    this software without specific prior written permission.
*
* THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
* "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
* LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS
* FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
* COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
* INCIDENTAL, SPECIAL, EXEMPLARY, OR CO<PERSON><PERSON>QUENTIAL DAMAGES
* (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
* SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
* HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
* STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
* IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
* POSSIBILITY OF SUCH DAMAGE.
*
* @file       bmi270_dsd.c
* @date       2025-04-22
* @version    v2.113.0
*
*/

/***************************************************************************/

/*!             Header files
 ****************************************************************************/
#include "bmi270_dsd.h"

/******************************************************************************/
/*!  @name         Structure Declaration                                      */
/******************************************************************************/

/*! @name Structure to define tap configuration */
struct bmi2_tap_config *bmi270_dsd_config;

/***************************************************************************/

/*!              Global Variable
 ****************************************************************************/

/*! @name  Global array that stores the configuration file of BMI270_DSD */
const uint8_t bmi270_dsd_config_file[] = {
    0xc8, 0x2e, 0x00, 0x2e, 0x80, 0x2e, 0x00, 0xb0, 0xc8, 0x2e, 0x00, 0x2e, 0xc8, 0x2e, 0x00, 0x2e, 0x80, 0x2e, 0x79,
    0xb6, 0x80, 0x2e, 0xe9, 0x00, 0xc8, 0x2e, 0x00, 0x2e, 0x80, 0x2e, 0x9f, 0x02, 0x50, 0x30, 0x21, 0x2e, 0x59, 0xf5,
    0x10, 0x30, 0x21, 0x2e, 0x6a, 0xf5, 0x80, 0x2e, 0xe5, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x6d, 0x01, 0x00, 0x22,
    0x00, 0x7f, 0x00, 0x00, 0x10, 0x00, 0x10, 0xd1, 0x00, 0x21, 0x4b, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1,
    0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00,
    0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e,
    0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80,
    0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1,
    0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00,
    0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e,
    0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80,
    0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1,
    0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00,
    0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0xaa, 0x57, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x01, 0x01, 0x00, 0x00, 0x01, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x1a, 0x24, 0x22, 0x00, 0x80, 0x2e, 0x73, 0x01, 0x20, 0x50, 0xe7, 0x7f, 0xf6, 0x7f, 0x46, 0x30, 0x0f,
    0x2e, 0xa4, 0xf1, 0xbe, 0x09, 0x80, 0xb3, 0x06, 0x2f, 0x0d, 0x2e, 0x8b, 0x00, 0x84, 0xaf, 0x02, 0x2f, 0x16, 0x30,
    0x2d, 0x2e, 0x85, 0x00, 0x86, 0x30, 0x2d, 0x2e, 0x60, 0xf5, 0xf6, 0x6f, 0xe7, 0x6f, 0xe0, 0x5f, 0xc8, 0x2e, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x6d, 0x00, 0x00,
    0x88, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 0x14, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc8, 0x00, 0x14, 0x28, 0x05, 0xe0, 0x90, 0x30, 0x05, 0xe0,
    0xaa, 0x38, 0x00, 0x00, 0x00, 0x00, 0xe1, 0x00, 0x92, 0x00, 0x00, 0x0c, 0xff, 0x0f, 0x00, 0x04, 0xc0, 0x00, 0xd7,
    0x00, 0x5b, 0xf5, 0x80, 0x00, 0x3f, 0xff, 0x19, 0xf4, 0x58, 0xf5, 0x97, 0x00, 0xe0, 0x00, 0x9a, 0x00, 0xff, 0x00,
    0xd8, 0x00, 0x1e, 0xf2, 0x70, 0x17, 0xc8, 0x00, 0x7f, 0xff, 0xff, 0xfc, 0xff, 0xfb, 0xa0, 0x0a, 0x00, 0xe4, 0x50,
    0x46, 0xb0, 0xb9, 0xca, 0x08, 0xd0, 0x07, 0x58, 0x02, 0xff, 0xfe, 0xdc, 0x05, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xce, 0x00, 0xfd, 0xf5, 0xd4, 0x00, 0xdc, 0x00, 0x00,
    0x40, 0x46, 0x0f, 0xeb, 0x00, 0xc2, 0xf5, 0x68, 0xf7, 0x40, 0x0f, 0x34, 0x0f, 0x3a, 0x0f, 0x59, 0x0f, 0x58, 0xf7,
    0x5b, 0xf7, 0x5c, 0x0f, 0x00, 0x80, 0xff, 0x7f, 0x86, 0x00, 0x4b, 0x0f, 0x5e, 0x0f, 0xb3, 0xf1, 0x58, 0x0f, 0x6c,
    0xf7, 0xb9, 0xf1, 0xc6, 0xf1, 0x00, 0xe0, 0x00, 0xff, 0xd1, 0xf5, 0x60, 0x0f, 0x63, 0x0f, 0xff, 0x03, 0x00, 0xfc,
    0xf0, 0x3f, 0x0b, 0x01, 0x1c, 0x01, 0x1a, 0x01, 0xb9, 0x00, 0x2d, 0xf5, 0xca, 0xf5, 0xdc, 0x00, 0xba, 0xf1, 0xb4,
    0xf1, 0x20, 0x50, 0x98, 0x2e, 0xbd, 0x0e, 0x50, 0x32, 0x98, 0x2e, 0x40, 0xb6, 0x02, 0x30, 0xf2, 0x7f, 0x00, 0x2e,
    0x00, 0x2e, 0xd0, 0x2e, 0x41, 0x50, 0x00, 0x2e, 0x81, 0x84, 0x88, 0xa2, 0xfb, 0x2f, 0x05, 0x2e, 0x10, 0x01, 0x2f,
    0xbd, 0xaf, 0xb8, 0x05, 0x2e, 0xe4, 0x00, 0x80, 0x90, 0x23, 0x2e, 0x19, 0x00, 0x07, 0x2f, 0x4d, 0x50, 0x02, 0x30,
    0x12, 0x42, 0x11, 0x30, 0x23, 0x2e, 0x82, 0x00, 0x02, 0x42, 0x05, 0x2d, 0x98, 0x2e, 0x91, 0x03, 0x10, 0x30, 0x21,
    0x2e, 0x82, 0x00, 0x01, 0x2e, 0x95, 0x00, 0x00, 0xb2, 0x12, 0x2f, 0x05, 0x2e, 0x0a, 0x01, 0x43, 0x52, 0x98, 0x2e,
    0xc7, 0xc1, 0x05, 0x2e, 0x18, 0x00, 0xf0, 0x7f, 0x80, 0xb2, 0x08, 0x2f, 0x43, 0x50, 0x98, 0x2e, 0x4d, 0xc3, 0x43,
    0x50, 0x98, 0x2e, 0x5a, 0xc7, 0x10, 0x30, 0x21, 0x2e, 0x82, 0x00, 0x01, 0x2e, 0x8b, 0x00, 0x04, 0xae, 0x0b, 0x2f,
    0x01, 0x2e, 0x95, 0x00, 0x00, 0xb2, 0x07, 0x2f, 0x43, 0x52, 0x98, 0x2e, 0x74, 0x0e, 0x00, 0xb2, 0x02, 0x2f, 0x10,
    0x30, 0x21, 0x2e, 0x87, 0x00, 0x01, 0x2e, 0x87, 0x00, 0x00, 0x90, 0x90, 0x2e, 0x62, 0x02, 0x01, 0x2e, 0x8e, 0x00,
    0x00, 0xb2, 0x04, 0x2f, 0x98, 0x2e, 0x15, 0x0e, 0x00, 0x30, 0x21, 0x2e, 0x85, 0x00, 0x01, 0x2e, 0x85, 0x00, 0x00,
    0xb2, 0x12, 0x2f, 0x01, 0x2e, 0x8b, 0x00, 0x00, 0x90, 0x02, 0x2f, 0x98, 0x2e, 0x05, 0x0e, 0x09, 0x2d, 0x98, 0x2e,
    0x5d, 0x0d, 0x01, 0x2e, 0x8b, 0x00, 0x04, 0x90, 0x02, 0x2f, 0x50, 0x32, 0x98, 0x2e, 0x40, 0xb6, 0x00, 0x30, 0x21,
    0x2e, 0x85, 0x00, 0x01, 0x2e, 0x86, 0x00, 0x00, 0xb2, 0x90, 0x2e, 0x7a, 0x02, 0x01, 0x2e, 0x86, 0x00, 0x02, 0x31,
    0x02, 0x08, 0x00, 0xb2, 0x04, 0x2f, 0x98, 0x2e, 0x47, 0xcb, 0x10, 0x30, 0x21, 0x2e, 0x82, 0x00, 0x82, 0x30, 0x01,
    0x2e, 0x86, 0x00, 0x02, 0x08, 0x00, 0xb2, 0x61, 0x2f, 0x05, 0x2e, 0x09, 0x01, 0x01, 0x2e, 0x8b, 0x00, 0x28, 0xbd,
    0x28, 0xb9, 0x05, 0xb2, 0x4f, 0x58, 0x23, 0x2f, 0x07, 0x90, 0x47, 0x52, 0x00, 0x30, 0x37, 0x2f, 0x15, 0x41, 0x04,
    0x41, 0xdc, 0xbe, 0x44, 0xbe, 0xdc, 0xba, 0x2c, 0x01, 0xa2, 0x00, 0x4f, 0x56, 0x51, 0x0f, 0x0c, 0x2f, 0xd2, 0x42,
    0x24, 0xb9, 0xc2, 0x42, 0x12, 0x30, 0x03, 0x2e, 0x6a, 0xf7, 0x9c, 0xbc, 0x9f, 0xb8, 0x40, 0xb2, 0x10, 0x22, 0x98,
    0x2e, 0x5a, 0xb6, 0x21, 0x2d, 0x62, 0x30, 0x25, 0x2e, 0x8b, 0x00, 0x98, 0x2e, 0x5a, 0xb6, 0x00, 0x30, 0x21, 0x2e,
    0x5a, 0xf5, 0x18, 0x2d, 0xe2, 0x7f, 0x50, 0x30, 0x98, 0x2e, 0x40, 0xb6, 0x4f, 0x54, 0x45, 0x50, 0x90, 0x42, 0x70,
    0x30, 0x4b, 0x52, 0x81, 0x42, 0xbe, 0x84, 0xe1, 0x6f, 0x40, 0xb2, 0x81, 0x42, 0x05, 0x2f, 0x21, 0x2e, 0x8b, 0x00,
    0x10, 0x30, 0x98, 0x2e, 0x5a, 0xb6, 0x03, 0x2d, 0x60, 0x30, 0x21, 0x2e, 0x8b, 0x00, 0x01, 0x2e, 0x8b, 0x00, 0x06,
    0x90, 0x18, 0x2f, 0x01, 0x2e, 0x80, 0x00, 0x49, 0x54, 0x45, 0x52, 0xe0, 0x7f, 0x98, 0x2e, 0x7a, 0xc1, 0xe2, 0x6f,
    0x10, 0x1a, 0x40, 0x30, 0x08, 0x2f, 0x21, 0x2e, 0x8b, 0x00, 0x20, 0x30, 0x98, 0x2e, 0x46, 0xb6, 0x50, 0x32, 0x98,
    0x2e, 0x40, 0xb6, 0x05, 0x2d, 0x98, 0x2e, 0x1e, 0x0e, 0x00, 0x30, 0x21, 0x2e, 0x8b, 0x00, 0x00, 0x30, 0x21, 0x2e,
    0x86, 0x00, 0x18, 0x2d, 0x01, 0x2e, 0x8b, 0x00, 0x03, 0xaa, 0x01, 0x2f, 0x98, 0x2e, 0x2b, 0x0e, 0x01, 0x2e, 0x8b,
    0x00, 0x3f, 0x80, 0x03, 0xa2, 0x01, 0x2f, 0x00, 0x2e, 0x02, 0x2d, 0x98, 0x2e, 0x41, 0x0e, 0x30, 0x30, 0x98, 0x2e,
    0x65, 0xb6, 0x00, 0x30, 0x21, 0x2e, 0x87, 0x00, 0x50, 0x32, 0x98, 0x2e, 0x40, 0xb6, 0x01, 0x2e, 0x82, 0x00, 0x00,
    0xb2, 0x17, 0x2f, 0x98, 0x2e, 0xf5, 0xcb, 0x05, 0x2e, 0x8c, 0x00, 0x03, 0x2e, 0x21, 0xf2, 0xf4, 0x6f, 0xf3, 0x31,
    0x02, 0x0a, 0x45, 0xbe, 0x4b, 0x08, 0x8c, 0x0a, 0x21, 0x2e, 0xd9, 0x00, 0xe2, 0x7f, 0x98, 0x2e, 0xa8, 0xcf, 0x06,
    0xbc, 0xe2, 0x6f, 0x10, 0x0a, 0x21, 0x2e, 0x21, 0xf2, 0x98, 0x2e, 0x0f, 0xb6, 0x02, 0x30, 0x25, 0x2e, 0xe4, 0x00,
    0x25, 0x2e, 0x82, 0x00, 0x25, 0x2e, 0x95, 0x00, 0x80, 0x2e, 0x7b, 0x01, 0x90, 0x50, 0xf7, 0x7f, 0xe6, 0x7f, 0xd5,
    0x7f, 0xc4, 0x7f, 0xb3, 0x7f, 0xa1, 0x7f, 0x90, 0x7f, 0x82, 0x7f, 0x7b, 0x7f, 0x98, 0x2e, 0xfb, 0xb5, 0x00, 0xb2,
    0x4f, 0x2f, 0x01, 0x2e, 0x1d, 0x01, 0x05, 0x2e, 0x1b, 0x01, 0x0f, 0xb8, 0x2f, 0xb9, 0x82, 0x0a, 0x25, 0x2e, 0x18,
    0x00, 0x05, 0x2e, 0xc1, 0xf5, 0x2e, 0xbd, 0x2e, 0xb9, 0x01, 0x2e, 0x81, 0x00, 0x31, 0x30, 0x8a, 0x04, 0x00, 0x90,
    0x0b, 0x2f, 0x01, 0x2e, 0x8b, 0x00, 0x04, 0xa2, 0x07, 0x2f, 0x01, 0x2e, 0x18, 0x00, 0x00, 0x90, 0x03, 0x2f, 0x01,
    0x2e, 0x84, 0x00, 0x00, 0xb2, 0x19, 0x2f, 0x55, 0x50, 0x43, 0x52, 0x98, 0x2e, 0x04, 0xb6, 0x05, 0x2e, 0x83, 0x00,
    0x25, 0x2e, 0x95, 0x00, 0x05, 0x2e, 0x83, 0x00, 0x80, 0x90, 0x02, 0x2f, 0x12, 0x30, 0x25, 0x2e, 0x83, 0x00, 0x01,
    0x2e, 0x84, 0x00, 0x00, 0xb2, 0x10, 0x30, 0x05, 0x2e, 0x18, 0x00, 0x01, 0x2f, 0x21, 0x2e, 0x18, 0x00, 0x25, 0x2e,
    0x84, 0x00, 0x05, 0x2e, 0x19, 0x00, 0x80, 0x90, 0x30, 0x30, 0x01, 0x30, 0x05, 0x2e, 0x96, 0x00, 0x41, 0x22, 0x0a,
    0x1a, 0x0c, 0x2f, 0x23, 0x2e, 0x96, 0x00, 0x57, 0x54, 0xc8, 0x08, 0x80, 0x40, 0x53, 0x52, 0xb6, 0xbd, 0x01, 0x08,
    0x03, 0x0a, 0x80, 0x42, 0x8a, 0x84, 0x51, 0x50, 0x80, 0x42, 0x00, 0x2e, 0x05, 0x2e, 0x8d, 0x00, 0x81, 0x82, 0x05,
    0x2e, 0x0a, 0x01, 0x23, 0x2e, 0x8d, 0x00, 0x49, 0x50, 0x90, 0x08, 0x01, 0x31, 0x23, 0x2e, 0x60, 0xf5, 0x80, 0xb2,
    0x0b, 0x2f, 0xf2, 0x3e, 0x01, 0x2e, 0xca, 0xf5, 0x82, 0x08, 0x25, 0x2e, 0xca, 0xf5, 0x05, 0x2e, 0x59, 0xf5, 0xe0,
    0x3f, 0x90, 0x08, 0x25, 0x2e, 0x59, 0xf5, 0x90, 0x6f, 0xb3, 0x6f, 0xc4, 0x6f, 0xd5, 0x6f, 0xe6, 0x6f, 0xf7, 0x6f,
    0x7b, 0x6f, 0x82, 0x6f, 0xa1, 0x6f, 0x70, 0x5f, 0xc8, 0x2e, 0x5d, 0x50, 0x80, 0x2e, 0x51, 0xb5, 0x42, 0x84, 0x03,
    0x40, 0xb6, 0xbd, 0xbe, 0xb9, 0xcb, 0x00, 0x84, 0x40, 0xc1, 0x40, 0xc4, 0x42, 0x81, 0x42, 0x13, 0x30, 0x00, 0x40,
    0x08, 0xbc, 0x0f, 0xb8, 0x00, 0xaa, 0xf0, 0x3f, 0x18, 0x22, 0x08, 0x18, 0x86, 0x42, 0xb8, 0x2e, 0x00, 0x2e, 0x42,
    0x40, 0xa5, 0xbd, 0xbf, 0xb9, 0xc0, 0xb2, 0x0c, 0x2f, 0x6d, 0x56, 0xd3, 0x08, 0x43, 0x42, 0x06, 0x84, 0x42, 0x82,
    0x03, 0x2c, 0x86, 0x86, 0x54, 0x40, 0x94, 0x42, 0x53, 0x1a, 0xfb, 0x2f, 0x80, 0x2e, 0xcd, 0xb3, 0xb8, 0x2e, 0x80,
    0x2e, 0x00, 0xc1, 0xfd, 0x2d, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x9a, 0x01,
    0x34, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x03, 0x2e, 0x10, 0x01, 0x1f, 0xbd, 0x9b, 0xbc, 0x50, 0x50, 0x2f, 0xb9, 0x9c, 0xb8, 0x80, 0xb2, 0xf1,
    0x7f, 0xeb, 0x7f, 0x37, 0x2f, 0x20, 0x25, 0x01, 0x2e, 0x89, 0x00, 0x00, 0xb2, 0x07, 0x2f, 0x5d, 0x50, 0xd2, 0x7f,
    0x98, 0x2e, 0x60, 0xb5, 0x00, 0x30, 0x21, 0x2e, 0x89, 0x00, 0xd2, 0x6f, 0x0a, 0x25, 0x3b, 0x86, 0x3c, 0x88, 0x03,
    0x2e, 0x88, 0x00, 0x5d, 0x50, 0x98, 0x2e, 0x7b, 0xb0, 0xc2, 0x6f, 0x80, 0x90, 0xf0, 0x6f, 0x03, 0x2f, 0x01, 0x30,
    0x98, 0x2e, 0x95, 0xcf, 0x05, 0x2d, 0x25, 0x2e, 0xd7, 0x00, 0x11, 0x30, 0x98, 0x2e, 0x95, 0xcf, 0x61, 0x54, 0xb1,
    0x6f, 0x40, 0x32, 0x5d, 0x56, 0xd8, 0x00, 0xa1, 0x42, 0x18, 0xb8, 0x85, 0x40, 0xc3, 0x40, 0xb2, 0xbd, 0x63, 0x54,
    0x5f, 0x58, 0xeb, 0x0a, 0x4c, 0x08, 0x93, 0x42, 0x04, 0x08, 0x91, 0x42, 0x90, 0x42, 0x00, 0x2e, 0x80, 0x40, 0x08,
    0x2c, 0x80, 0x42, 0xf0, 0x6f, 0x01, 0x30, 0x98, 0x2e, 0x95, 0xcf, 0x10, 0x30, 0x21, 0x2e, 0x89, 0x00, 0x00, 0x2e,
    0xeb, 0x6f, 0xb0, 0x5f, 0xb8, 0x2e, 0xa0, 0x50, 0x3a, 0x25, 0xf6, 0x86, 0xe1, 0x7f, 0x12, 0x80, 0x83, 0x88, 0xf3,
    0x7f, 0xdb, 0x7f, 0x41, 0x36, 0x95, 0x40, 0x29, 0x18, 0x15, 0x40, 0xb5, 0x05, 0x15, 0x40, 0xd6, 0x42, 0x7d, 0x07,
    0xd5, 0x42, 0x54, 0x0e, 0xf5, 0x2f, 0x28, 0x80, 0xf2, 0x6f, 0xe1, 0x6f, 0xc0, 0x7f, 0x98, 0x2e, 0xa4, 0xb1, 0xf2,
    0x6f, 0xe1, 0x6f, 0xc0, 0x6f, 0x98, 0x2e, 0x09, 0xb2, 0xdb, 0x6f, 0x60, 0x5f, 0xb8, 0x2e, 0xc0, 0x50, 0xe7, 0x7f,
    0xf6, 0x7f, 0x26, 0x30, 0x0f, 0x2e, 0x61, 0xf5, 0x2f, 0x2e, 0x86, 0x00, 0x0f, 0x2e, 0x86, 0x00, 0xbe, 0x09, 0xa2,
    0x7f, 0x91, 0x7f, 0x80, 0x7f, 0x80, 0xb3, 0xd5, 0x7f, 0xc4, 0x7f, 0xb3, 0x7f, 0x7b, 0x7f, 0x11, 0x2f, 0x59, 0x50,
    0x1a, 0x25, 0x12, 0x40, 0x42, 0x7f, 0x74, 0x82, 0x12, 0x40, 0x52, 0x7f, 0x00, 0x2e, 0x00, 0x40, 0x60, 0x7f, 0x98,
    0x2e, 0x6a, 0xd6, 0x01, 0x2e, 0x61, 0xf7, 0x01, 0x31, 0x01, 0x0a, 0x21, 0x2e, 0x61, 0xf7, 0x80, 0x30, 0x03, 0x2e,
    0x86, 0x00, 0x08, 0x08, 0x00, 0xb2, 0x40, 0x2f, 0x03, 0x2e, 0x09, 0x01, 0x17, 0xbc, 0x96, 0xbc, 0x0f, 0xb8, 0x9f,
    0xb8, 0x40, 0x90, 0x21, 0x2e, 0x8f, 0x00, 0x10, 0x30, 0x01, 0x30, 0x2a, 0x2f, 0x03, 0x2e, 0x8b, 0x00, 0x44, 0xb2,
    0x05, 0x2f, 0x47, 0xb2, 0x00, 0x30, 0x2d, 0x2f, 0x21, 0x2e, 0x86, 0x00, 0x2b, 0x2d, 0x03, 0x2e, 0xfd, 0xf5, 0x9e,
    0xbc, 0x9f, 0xb8, 0x40, 0x90, 0x14, 0x2f, 0x03, 0x2e, 0xfc, 0xf5, 0x99, 0xbc, 0x9f, 0xb8, 0x40, 0x90, 0x0e, 0x2f,
    0x03, 0x2e, 0x49, 0xf1, 0x5b, 0x54, 0x4a, 0x08, 0x40, 0x90, 0x08, 0x2f, 0x98, 0x2e, 0xfb, 0xb5, 0x00, 0xb2, 0x10,
    0x30, 0x03, 0x2f, 0x50, 0x30, 0x21, 0x2e, 0x8b, 0x00, 0x10, 0x2d, 0x98, 0x2e, 0x46, 0xb6, 0x00, 0x30, 0x21, 0x2e,
    0x86, 0x00, 0x0a, 0x2d, 0x05, 0x2e, 0x69, 0xf7, 0x2d, 0xbd, 0x2f, 0xb9, 0x80, 0xb2, 0x01, 0x2f, 0x21, 0x2e, 0x87,
    0x00, 0x23, 0x2e, 0x86, 0x00, 0xe0, 0x31, 0x21, 0x2e, 0x61, 0xf5, 0xf6, 0x6f, 0xe7, 0x6f, 0x80, 0x6f, 0xa2, 0x6f,
    0xb3, 0x6f, 0xc4, 0x6f, 0xd5, 0x6f, 0x7b, 0x6f, 0x91, 0x6f, 0x40, 0x5f, 0xc8, 0x2e, 0x05, 0x30, 0xf6, 0x32, 0x05,
    0x43, 0xc6, 0x01, 0xc5, 0x42, 0xc5, 0x43, 0x80, 0x50, 0x86, 0x40, 0x80, 0x91, 0x82, 0x8c, 0xf5, 0x8f, 0x09, 0x2f,
    0x81, 0x8a, 0x00, 0x2e, 0x45, 0x41, 0x40, 0x91, 0x05, 0x30, 0x03, 0x2f, 0x85, 0x41, 0x40, 0xb3, 0x05, 0x30, 0x3e,
    0x2f, 0x45, 0x40, 0xd9, 0xbe, 0xde, 0xba, 0xf0, 0x7f, 0xeb, 0x7f, 0x40, 0xb3, 0xd3, 0x7f, 0xc4, 0x7f, 0xb7, 0x7f,
    0xa6, 0x7f, 0x92, 0x7f, 0x07, 0x2f, 0x42, 0x91, 0x01, 0x25, 0x02, 0x2f, 0xeb, 0x6f, 0x80, 0x5f, 0xb8, 0x2e, 0x0a,
    0x2c, 0x91, 0x6f, 0xf0, 0x6f, 0x23, 0x33, 0xc3, 0x00, 0x04, 0x30, 0xeb, 0x6f, 0x80, 0x5f, 0xc4, 0x42, 0x80, 0x2e,
    0xdf, 0xb0, 0x50, 0x25, 0x98, 0x2e, 0x26, 0x03, 0x15, 0x25, 0xf0, 0x6f, 0x81, 0x7f, 0x98, 0x2e, 0x39, 0x03, 0x92,
    0x6f, 0x81, 0x6f, 0xf0, 0x6f, 0x98, 0x2e, 0xdf, 0x03, 0x92, 0x6f, 0x81, 0x6f, 0xf0, 0x6f, 0x98, 0x2e, 0x4b, 0xb2,
    0xa0, 0x6f, 0x81, 0x6f, 0x02, 0x40, 0xf0, 0x6f, 0x98, 0x2e, 0x7f, 0xb3, 0xb7, 0x6f, 0x82, 0x6f, 0xf1, 0x6f, 0x98,
    0x2e, 0x8c, 0xb3, 0x50, 0x25, 0xf0, 0x6f, 0xd3, 0x6f, 0xc4, 0x6f, 0xeb, 0x6f, 0xc1, 0x41, 0x40, 0x90, 0x03, 0x2f,
    0x00, 0x30, 0x00, 0x43, 0x07, 0x2c, 0xc0, 0x42, 0x51, 0x32, 0x05, 0x43, 0x01, 0x00, 0x00, 0x2e, 0x00, 0x40, 0xc0,
    0x42, 0x80, 0x5f, 0xb8, 0x2e, 0xb3, 0x32, 0xc3, 0x00, 0x90, 0x50, 0xc5, 0x40, 0x65, 0x5e, 0xf1, 0x7f, 0x6f, 0x0e,
    0x44, 0x40, 0x07, 0x2f, 0xf2, 0x39, 0xa2, 0x08, 0x01, 0x34, 0xf0, 0x6f, 0x91, 0x0a, 0x02, 0x42, 0x80, 0x2e, 0xa2,
    0xb1, 0x41, 0x8b, 0x67, 0x5e, 0x0c, 0x82, 0xc5, 0x42, 0x6f, 0x0e, 0x90, 0x2e, 0x97, 0xb1, 0x46, 0x86, 0xe3, 0x7f,
    0x2f, 0x1a, 0x03, 0x30, 0x90, 0x2e, 0x85, 0xb1, 0x69, 0x5a, 0x25, 0x09, 0x1c, 0x80, 0xd4, 0x7f, 0xc0, 0x7f, 0x47,
    0x36, 0x04, 0x30, 0x50, 0x40, 0x96, 0x40, 0xb4, 0x7f, 0x79, 0x88, 0x37, 0x18, 0x15, 0x41, 0x41, 0x40, 0xa5, 0x7f,
    0x75, 0x05, 0x06, 0x41, 0x96, 0x7f, 0xfe, 0x07, 0x7f, 0xbf, 0x05, 0x89, 0xd1, 0xba, 0x6e, 0x0b, 0x05, 0x00, 0xf1,
    0xb6, 0x4d, 0x02, 0x58, 0x05, 0xd9, 0x07, 0x77, 0x7f, 0x13, 0x30, 0x85, 0x7f, 0x40, 0xa8, 0x10, 0x43, 0x11, 0x43,
    0x0a, 0x2f, 0x40, 0x90, 0x03, 0x30, 0x03, 0x2f, 0x00, 0xaa, 0x13, 0x30, 0x00, 0x2f, 0x03, 0x30, 0xc0, 0x90, 0x13,
    0x30, 0x00, 0x2f, 0x03, 0x30, 0xc0, 0x90, 0x50, 0x25, 0x71, 0x25, 0x01, 0x2f, 0x77, 0x6f, 0x85, 0x6f, 0x26, 0x33,
    0x3e, 0x0f, 0x04, 0x2f, 0x26, 0x33, 0x7e, 0x1a, 0x44, 0x2f, 0x40, 0xa7, 0x42, 0x2f, 0xc0, 0x90, 0x14, 0x30, 0x12,
    0x30, 0x00, 0x2f, 0x02, 0x30, 0xb5, 0x6f, 0x37, 0x30, 0x6f, 0x09, 0x94, 0x08, 0x27, 0xbd, 0xd7, 0x6f, 0xba, 0x0a,
    0x6b, 0x5e, 0x97, 0x08, 0xd8, 0xbe, 0x95, 0x0a, 0xf5, 0x39, 0x55, 0x09, 0x07, 0x32, 0xf2, 0x6f, 0x6f, 0x0b, 0x85,
    0x42, 0xc0, 0x90, 0x01, 0x2f, 0x80, 0x6f, 0x71, 0x6f, 0xc2, 0x6f, 0xc0, 0x90, 0x90, 0x42, 0x81, 0x42, 0xbb, 0x84,
    0x00, 0x2f, 0xf4, 0x3f, 0xa0, 0x6f, 0x44, 0x18, 0x93, 0x6f, 0x4c, 0x16, 0xdc, 0x18, 0xc8, 0x18, 0x7f, 0xbc, 0xe1,
    0xb8, 0x08, 0x0b, 0xf1, 0xb5, 0xc1, 0xbc, 0xb1, 0xbe, 0x4f, 0xb8, 0x94, 0x42, 0x71, 0x04, 0x93, 0x42, 0x28, 0x0a,
    0x91, 0x42, 0x38, 0x06, 0x80, 0x42, 0xb1, 0x82, 0xe2, 0x6f, 0x00, 0x30, 0x50, 0x42, 0x40, 0x42, 0x79, 0x82, 0x00,
    0x2e, 0x50, 0x42, 0x40, 0x42, 0x47, 0x82, 0x4a, 0x0e, 0xf6, 0x2f, 0x70, 0x5f, 0xb8, 0x2e, 0xb0, 0x6f, 0x03, 0x30,
    0x47, 0x36, 0x14, 0x25, 0x01, 0x88, 0x03, 0xa3, 0x80, 0x2f, 0x00, 0x2e, 0x1d, 0x2d, 0xe2, 0x6f, 0x55, 0x40, 0x40,
    0x40, 0x0f, 0xbe, 0x79, 0x82, 0xd1, 0xba, 0x2c, 0x0b, 0x54, 0x42, 0x01, 0xb4, 0x40, 0x42, 0x45, 0x82, 0x00, 0x2e,
    0x53, 0x42, 0x53, 0x42, 0x4a, 0x0e, 0xf0, 0x2f, 0x00, 0x2e, 0x0b, 0x2d, 0x83, 0x80, 0x93, 0x40, 0x44, 0x40, 0x23,
    0x01, 0x54, 0x42, 0xcb, 0x16, 0x44, 0x40, 0xe3, 0x02, 0x53, 0x42, 0x50, 0x0e, 0xf5, 0x2f, 0x70, 0x5f, 0xb8, 0x2e,
    0x41, 0x88, 0x52, 0x25, 0x02, 0x41, 0x28, 0xbd, 0xa8, 0xb9, 0x5b, 0x18, 0x01, 0x30, 0xcb, 0x18, 0x80, 0x50, 0x44,
    0x83, 0xa1, 0x7f, 0xb0, 0x7f, 0xf8, 0xbc, 0x68, 0xb8, 0x08, 0x0a, 0xb4, 0xbf, 0xa1, 0x30, 0x26, 0x25, 0xbc, 0xb9,
    0x79, 0x18, 0x08, 0x89, 0xd9, 0x18, 0x01, 0x41, 0x9a, 0xb8, 0xf7, 0x7f, 0xe6, 0x7f, 0x43, 0x36, 0x0b, 0x18, 0xa8,
    0xbc, 0x96, 0x7f, 0x4b, 0x18, 0x51, 0x41, 0xc3, 0x18, 0x52, 0x41, 0x53, 0x41, 0x54, 0x41, 0xc6, 0x7f, 0x65, 0x25,
    0xd7, 0x7f, 0x8b, 0x7f, 0x00, 0x2e, 0x95, 0x41, 0x86, 0x41, 0x98, 0x2e, 0xf4, 0xb4, 0xb1, 0x6f, 0x42, 0x32, 0x0a,
    0x01, 0x00, 0xb2, 0x02, 0x85, 0x63, 0x32, 0xcb, 0x00, 0x04, 0x41, 0x00, 0x30, 0x01, 0x2f, 0x00, 0xb3, 0x18, 0x2f,
    0xa6, 0x6f, 0x00, 0x2e, 0x95, 0x41, 0x86, 0x41, 0x80, 0xa9, 0x05, 0x2f, 0x80, 0x91, 0x01, 0x2f, 0x40, 0xab, 0x01,
    0x2f, 0x45, 0x05, 0x86, 0x07, 0x80, 0xa1, 0x04, 0x2f, 0x80, 0x91, 0x04, 0x2f, 0x96, 0x6f, 0xee, 0x0f, 0x01, 0x2f,
    0x00, 0x91, 0x03, 0x2f, 0xc0, 0x42, 0xc8, 0x86, 0x04, 0x2c, 0xc0, 0x42, 0xc0, 0x40, 0x01, 0x80, 0xc0, 0x42, 0x00,
    0x2e, 0x82, 0x40, 0x67, 0x50, 0x50, 0x1a, 0x06, 0x2f, 0xe0, 0x32, 0x08, 0x00, 0x11, 0x30, 0x01, 0x42, 0x38, 0x80,
    0xbf, 0x82, 0x01, 0x42, 0x00, 0x2e, 0x8b, 0x6f, 0x80, 0x5f, 0xb8, 0x2e, 0x70, 0x50, 0x62, 0x25, 0x21, 0x25, 0xa0,
    0x7f, 0x88, 0x88, 0x71, 0x50, 0x91, 0x41, 0x04, 0x41, 0xc0, 0x7f, 0xbb, 0x7f, 0x46, 0xbc, 0x92, 0x41, 0x93, 0x41,
    0x0b, 0x30, 0x94, 0x41, 0xfb, 0x7f, 0x06, 0xb8, 0x95, 0x41, 0x6f, 0x5e, 0x0b, 0x37, 0x86, 0x41, 0x90, 0x7f, 0xe7,
    0x7f, 0xdb, 0x7f, 0x98, 0x2e, 0xf4, 0xb4, 0xa3, 0x6f, 0x51, 0x32, 0x99, 0x00, 0x94, 0x32, 0xa1, 0x32, 0x1c, 0x01,
    0x59, 0x00, 0x83, 0x40, 0x00, 0xb2, 0x02, 0x30, 0x02, 0x2f, 0x90, 0x6f, 0x18, 0x0e, 0x04, 0x2f, 0x12, 0x43, 0x3d,
    0x83, 0x02, 0x43, 0x12, 0x2c, 0x42, 0x42, 0x00, 0x41, 0x01, 0x80, 0x00, 0x43, 0x3e, 0x81, 0x95, 0x6f, 0x04, 0x40,
    0x55, 0x05, 0x01, 0x89, 0x1d, 0x0e, 0x04, 0x42, 0x02, 0x2f, 0x00, 0x2e, 0x04, 0x2c, 0x42, 0x42, 0x42, 0x40, 0x81,
    0x84, 0x42, 0x42, 0x00, 0x2e, 0xbb, 0x6f, 0x90, 0x5f, 0xb8, 0x2e, 0xe4, 0x32, 0xc4, 0x00, 0x50, 0x50, 0xc4, 0x40,
    0xfa, 0x86, 0x01, 0xb3, 0xfb, 0x7f, 0xe1, 0x7f, 0xd0, 0x7f, 0xc3, 0x7f, 0x0b, 0x2f, 0x82, 0x32, 0x02, 0x01, 0x06,
    0x82, 0x02, 0x30, 0x02, 0x2c, 0x02, 0x43, 0x12, 0x42, 0x41, 0x1a, 0xfc, 0x2f, 0x98, 0x2e, 0x7e, 0xb5, 0x1d, 0x2d,
    0x83, 0x82, 0x94, 0x40, 0x03, 0x40, 0xdc, 0x00, 0x13, 0x42, 0x0c, 0x17, 0x03, 0x40, 0x1c, 0x03, 0x14, 0x42, 0x51,
    0x0e, 0xf5, 0x2f, 0xbd, 0x82, 0xb0, 0x7f, 0x00, 0x2e, 0x50, 0x40, 0x52, 0x40, 0x44, 0x40, 0x48, 0x16, 0xca, 0x16,
    0x4c, 0x17, 0x98, 0x2e, 0x87, 0xb5, 0xb0, 0x6f, 0x22, 0x32, 0x02, 0x00, 0x00, 0x2e, 0x02, 0x40, 0x81, 0x84, 0x02,
    0x42, 0x00, 0x2e, 0xc1, 0x6f, 0x67, 0x50, 0x42, 0x40, 0x50, 0x1a, 0x7c, 0x82, 0xd0, 0x6f, 0xc1, 0x7f, 0x02, 0x2f,
    0xe1, 0x6f, 0x98, 0x2e, 0xfc, 0xb3, 0xc0, 0x6f, 0x00, 0x2e, 0x00, 0x40, 0x00, 0x90, 0x22, 0x2f, 0xd0, 0x6f, 0x0a,
    0x82, 0x00, 0x2e, 0x52, 0x40, 0x41, 0x40, 0x40, 0xa8, 0x06, 0x2f, 0x40, 0x90, 0x01, 0x2f, 0x80, 0xaa, 0x02, 0x2f,
    0x04, 0x30, 0xa2, 0x04, 0x61, 0x06, 0x40, 0xa8, 0x03, 0x2f, 0x40, 0x90, 0x10, 0x2f, 0x80, 0xa6, 0x0e, 0x2f, 0x12,
    0x33, 0x42, 0x00, 0x12, 0x30, 0x52, 0x42, 0x00, 0x2e, 0x41, 0x40, 0x40, 0xb2, 0x06, 0x2f, 0x41, 0x32, 0x01, 0x00,
    0x38, 0x82, 0x04, 0x30, 0x54, 0x42, 0x02, 0x42, 0x44, 0x42, 0x00, 0x2e, 0xfb, 0x6f, 0xb0, 0x5f, 0xb8, 0x2e, 0x60,
    0x50, 0x1c, 0x88, 0xf0, 0x7f, 0xe4, 0x7f, 0x23, 0x33, 0x10, 0x41, 0x3b, 0x8b, 0x0b, 0x18, 0x51, 0x41, 0x43, 0x87,
    0x02, 0x41, 0x06, 0x00, 0x97, 0x02, 0x41, 0x04, 0x45, 0x41, 0xd1, 0x42, 0x55, 0x07, 0xc5, 0x42, 0xc5, 0x8a, 0x21,
    0x30, 0x52, 0x41, 0x77, 0x87, 0x45, 0x41, 0xd7, 0x40, 0xc7, 0x80, 0x97, 0x00, 0xc3, 0x40, 0x12, 0x42, 0x6b, 0x03,
    0x05, 0x42, 0x0e, 0x8a, 0x71, 0x87, 0x42, 0x41, 0xd0, 0x40, 0xf9, 0x8a, 0x80, 0x90, 0x42, 0x31, 0xd5, 0x7f, 0x91,
    0x22, 0xc3, 0x40, 0x51, 0x41, 0xc0, 0xa8, 0x46, 0x41, 0xcb, 0x7f, 0x1b, 0x2f, 0xc0, 0x90, 0x01, 0x2f, 0xd0, 0x0e,
    0x17, 0x2f, 0xb5, 0x7f, 0x05, 0x30, 0xea, 0x05, 0xa6, 0x7f, 0xad, 0x07, 0x1e, 0x0e, 0x03, 0x2f, 0x5e, 0x1a, 0x0c,
    0x2f, 0x47, 0x0f, 0x0a, 0x2f, 0x05, 0x8f, 0x02, 0x00, 0xdd, 0x02, 0x41, 0x82, 0xa6, 0x6f, 0xe2, 0x6f, 0xd0, 0x43,
    0xb5, 0x03, 0xc3, 0x43, 0x81, 0x42, 0x06, 0x43, 0x0e, 0x2c, 0xb0, 0x6f, 0x05, 0x8f, 0x82, 0x04, 0x05, 0x25, 0x05,
    0x30, 0xdd, 0x06, 0xd2, 0x43, 0xc3, 0x43, 0x7f, 0x82, 0xf2, 0x3f, 0xe3, 0x6f, 0xb2, 0x02, 0xc1, 0x42, 0x02, 0x43,
    0x54, 0x30, 0xd2, 0x6f, 0x03, 0x40, 0x82, 0x40, 0x54, 0x18, 0xdc, 0x18, 0xf3, 0x6f, 0x51, 0x32, 0x59, 0x00, 0xed,
    0xb9, 0x73, 0xbd, 0xda, 0x0a, 0x08, 0x80, 0x73, 0x54, 0x03, 0x42, 0x9a, 0x0e, 0x77, 0x56, 0xf0, 0x7f, 0xe1, 0x7f,
    0x09, 0x2f, 0x42, 0x42, 0x77, 0x82, 0xd1, 0x7f, 0x02, 0x30, 0x98, 0x2e, 0x79, 0xca, 0xd5, 0x6f, 0x00, 0x2e, 0x50,
    0x43, 0x41, 0x43, 0x00, 0x2e, 0xf5, 0x6f, 0x75, 0x58, 0x45, 0x41, 0xac, 0x0f, 0x0f, 0x2f, 0xe1, 0x6f, 0x05, 0x30,
    0x44, 0x42, 0x77, 0x88, 0xf4, 0x7f, 0x54, 0x30, 0x77, 0x56, 0x02, 0x30, 0x98, 0x2e, 0x79, 0xca, 0x05, 0x30, 0xf4,
    0x6f, 0xe8, 0x04, 0x13, 0x43, 0x69, 0x07, 0x05, 0x43, 0x00, 0x2e, 0xcb, 0x6f, 0xa0, 0x5f, 0xb8, 0x2e, 0x92, 0x32,
    0x02, 0x01, 0x54, 0x25, 0x13, 0x41, 0x79, 0x54, 0x5a, 0x1a, 0xc2, 0x32, 0x82, 0x01, 0x02, 0x30, 0x13, 0x30, 0x05,
    0x2f, 0x86, 0x41, 0x80, 0x91, 0x02, 0x2f, 0x46, 0x8d, 0x42, 0x43, 0x83, 0x43, 0x00, 0x2e, 0x05, 0x41, 0x7b, 0x5c,
    0x05, 0x89, 0x6e, 0x0e, 0x06, 0x2f, 0xa5, 0x32, 0x85, 0x01, 0x82, 0x8b, 0x43, 0x8f, 0x42, 0x43, 0x82, 0x43, 0xc3,
    0x43, 0x41, 0x82, 0x00, 0x2e, 0x45, 0x40, 0x57, 0xbf, 0x6f, 0xbb, 0x81, 0x91, 0x06, 0x2f, 0xf7, 0x32, 0x7d, 0x5c,
    0xc7, 0x01, 0x6e, 0x09, 0xc3, 0x43, 0x45, 0x42, 0x00, 0x2e, 0x01, 0x41, 0x40, 0xb2, 0x09, 0x2f, 0x51, 0x32, 0x41,
    0x00, 0x77, 0x80, 0x42, 0x42, 0x12, 0x42, 0x05, 0x82, 0x02, 0x42, 0x52, 0x42, 0x42, 0x42, 0xb8, 0x2e, 0xb8, 0x2e,
    0x30, 0x50, 0xf1, 0x7f, 0xe0, 0x7f, 0xdb, 0x7f, 0x12, 0x25, 0x98, 0x2e, 0xb7, 0xb2, 0xdb, 0x6f, 0xf1, 0x6f, 0xe0,
    0x6f, 0xd0, 0x5f, 0x80, 0x2e, 0x45, 0xb3, 0x88, 0x80, 0x72, 0x32, 0x4a, 0x01, 0x00, 0x40, 0x06, 0xbd, 0x40, 0x41,
    0x26, 0xb9, 0xc6, 0x32, 0x33, 0x32, 0xc4, 0x33, 0x8e, 0x01, 0x46, 0x8b, 0x93, 0x00, 0x44, 0x0f, 0x0b, 0x2f, 0x50,
    0x32, 0xc8, 0x00, 0x20, 0x30, 0xc3, 0x40, 0x9a, 0x0f, 0x02, 0x2f, 0x00, 0x2e, 0x09, 0x2c, 0x80, 0x43, 0x10, 0x30,
    0x06, 0x2c, 0x80, 0x43, 0xbb, 0x85, 0x3f, 0x80, 0x03, 0x30, 0x83, 0x43, 0x80, 0x42, 0xd2, 0x32, 0x60, 0x41, 0x8a,
    0x00, 0x00, 0x90, 0x43, 0x41, 0x01, 0x2f, 0xc1, 0xb2, 0x14, 0x2f, 0x01, 0x90, 0x10, 0x2f, 0xc0, 0x90, 0x0e, 0x2f,
    0x83, 0x42, 0xb7, 0x84, 0x10, 0x30, 0x82, 0x40, 0x80, 0x90, 0x0d, 0x2f, 0x22, 0x33, 0x4a, 0x00, 0x82, 0x3d, 0x40,
    0x42, 0x4a, 0x00, 0x02, 0x30, 0x52, 0x42, 0x42, 0x42, 0xb8, 0x2e, 0x00, 0x30, 0xb8, 0x2e, 0xc0, 0x2e, 0x83, 0x42,
    0x20, 0x30, 0xb8, 0x2e, 0x0a, 0x80, 0x02, 0x82, 0x12, 0x40, 0x03, 0x40, 0x70, 0x50, 0x0d, 0x80, 0xd0, 0x7f, 0xc3,
    0x7f, 0xb1, 0x7f, 0xa2, 0x7f, 0x9b, 0x7f, 0x44, 0x31, 0x05, 0x30, 0x98, 0x2e, 0xf1, 0xcf, 0x42, 0x31, 0x42, 0x18,
    0xca, 0x18, 0xd2, 0x6f, 0xa3, 0x6f, 0x90, 0x42, 0x91, 0x42, 0xde, 0x04, 0xc0, 0x6f, 0x93, 0x42, 0x47, 0x06, 0x81,
    0x42, 0xab, 0x82, 0x00, 0x2e, 0x52, 0x40, 0x4b, 0x80, 0xd0, 0x7f, 0xa4, 0x30, 0x05, 0x30, 0x43, 0x40, 0x98, 0x2e,
    0x79, 0xca, 0xd2, 0x6f, 0xb3, 0x6f, 0x90, 0x42, 0x81, 0x42, 0xb5, 0x82, 0x4b, 0x0e, 0xf0, 0x2f, 0x9b, 0x6f, 0x90,
    0x5f, 0xb8, 0x2e, 0x20, 0x51, 0x5a, 0x25, 0x6e, 0x8b, 0xa1, 0x7f, 0xb0, 0x7f, 0x07, 0x84, 0xd5, 0x7f, 0xcb, 0x7f,
    0x51, 0x30, 0x14, 0x40, 0x02, 0x86, 0x00, 0x40, 0x61, 0x18, 0xc1, 0x18, 0x56, 0x43, 0xff, 0x80, 0x5a, 0x0e, 0x57,
    0x43, 0xf5, 0x2f, 0x31, 0x6f, 0x27, 0x6f, 0x04, 0x6f, 0xf3, 0x6e, 0xe2, 0x6e, 0x15, 0x6f, 0x97, 0x7f, 0x81, 0x7f,
    0xe7, 0x7f, 0xf1, 0x7f, 0x70, 0x7f, 0x98, 0x2e, 0xb2, 0xb5, 0x75, 0x6f, 0x5a, 0x8b, 0x00, 0x2e, 0x50, 0x43, 0x41,
    0x43, 0x52, 0x8b, 0x6d, 0x85, 0x40, 0x41, 0x00, 0x90, 0x0c, 0x2f, 0xa0, 0x6f, 0x09, 0x80, 0x45, 0x36, 0x00, 0x40,
    0x06, 0xbc, 0x06, 0xb8, 0x45, 0x18, 0xb1, 0x6f, 0x05, 0x30, 0x5e, 0x82, 0xe8, 0x18, 0x56, 0x42, 0x47, 0x42, 0x06,
    0x33, 0x90, 0x40, 0xbd, 0x8e, 0xb4, 0x6f, 0xd5, 0x41, 0xc1, 0x41, 0x06, 0x87, 0x82, 0x40, 0xe1, 0x8f, 0x26, 0x01,
    0x11, 0x0e, 0x73, 0x7f, 0x67, 0x7f, 0x08, 0x2f, 0x51, 0x1a, 0x01, 0x2f, 0x45, 0x0e, 0x04, 0x2f, 0x05, 0x30, 0xb0,
    0x6f, 0x05, 0x43, 0x80, 0x2e, 0xe8, 0xb4, 0xa0, 0x3d, 0x60, 0x01, 0x01, 0x41, 0x50, 0x41, 0x45, 0x41, 0x41, 0x82,
    0x01, 0x43, 0x40, 0xa9, 0x65, 0x25, 0x20, 0x25, 0x06, 0x2f, 0x40, 0x91, 0x01, 0x2f, 0x00, 0xaa, 0x02, 0x2f, 0x01,
    0x30, 0x88, 0x04, 0x8d, 0x07, 0xc6, 0x82, 0x80, 0xa9, 0x17, 0x30, 0x05, 0x2f, 0x80, 0x91, 0x17, 0x30, 0x44, 0x2f,
    0x80, 0xa6, 0x17, 0x30, 0x41, 0x2f, 0x02, 0x41, 0x81, 0xa6, 0x07, 0x30, 0x3d, 0x2f, 0x97, 0x6f, 0x87, 0x04, 0x86,
    0x6f, 0x2e, 0x07, 0x00, 0xa9, 0x05, 0x2f, 0x00, 0x91, 0x01, 0x2f, 0x80, 0xaa, 0x01, 0x2f, 0xb8, 0x04, 0x35, 0x07,
    0x00, 0xa1, 0x07, 0x30, 0x06, 0x2f, 0x00, 0x91, 0x07, 0x30, 0x2b, 0x2f, 0x7f, 0x50, 0xd0, 0x0f, 0x07, 0x30, 0x27,
    0x2f, 0x91, 0x7f, 0x00, 0x2e, 0xd6, 0x6f, 0x86, 0x7f, 0x05, 0x30, 0xd2, 0x40, 0x94, 0x41, 0xad, 0xb8, 0x56, 0x7f,
    0x41, 0xbc, 0xd7, 0x40, 0x23, 0xbd, 0x86, 0x41, 0xf3, 0xbf, 0x90, 0x00, 0x4f, 0xba, 0x61, 0xbf, 0x39, 0x0a, 0x74,
    0x0a, 0x43, 0x7f, 0xc1, 0x02, 0xa4, 0x30, 0x98, 0x2e, 0x79, 0xca, 0x56, 0x6f, 0x82, 0x6f, 0x43, 0x6f, 0x95, 0x6f,
    0x91, 0x43, 0x80, 0x42, 0x5d, 0x0e, 0xe2, 0x2f, 0x40, 0x32, 0x18, 0x00, 0x73, 0x6f, 0x05, 0x40, 0x7f, 0x8b, 0x05,
    0x42, 0x17, 0x30, 0x91, 0x6f, 0xc0, 0xb3, 0x3a, 0x2f, 0xd0, 0x6f, 0x00, 0x2e, 0x15, 0x40, 0x12, 0x40, 0xd5, 0x42,
    0xd2, 0x42, 0x59, 0x0e, 0xf9, 0x2f, 0xf4, 0x80, 0xd0, 0x7f, 0x98, 0x2e, 0xcd, 0xb3, 0xd0, 0x6f, 0x25, 0x33, 0x05,
    0x00, 0xa5, 0x6f, 0x11, 0x40, 0x40, 0xb2, 0x07, 0x2f, 0x42, 0x8b, 0x72, 0x6f, 0x03, 0x2c, 0x46, 0x83, 0x93, 0x40,
    0x53, 0x43, 0x69, 0x1a, 0xfb, 0x2f, 0x00, 0x40, 0x00, 0x90, 0xb0, 0x6f, 0x1c, 0x2f, 0x35, 0x33, 0x45, 0x01, 0x6d,
    0x83, 0x1b, 0x30, 0x57, 0x40, 0x4b, 0x43, 0x7d, 0x8a, 0x41, 0x40, 0x11, 0xbe, 0x15, 0x25, 0xff, 0xb9, 0x71, 0xbf,
    0x52, 0x41, 0x47, 0x41, 0xe3, 0x0a, 0x1f, 0x0e, 0xb6, 0x25, 0x06, 0x2f, 0x5f, 0x1a, 0x02, 0x2f, 0x4b, 0x25, 0x62,
    0x0e, 0x01, 0x2f, 0xb2, 0x25, 0x37, 0x25, 0x43, 0x43, 0x02, 0x2c, 0x4b, 0x42, 0xb0, 0x6f, 0x05, 0x30, 0x73, 0x6f,
    0x02, 0x2c, 0xcb, 0x6f, 0x15, 0x42, 0x43, 0x1a, 0xfc, 0x2f, 0x60, 0x6f, 0x81, 0x32, 0x01, 0x00, 0xe0, 0x5e, 0x05,
    0x42, 0x80, 0x2e, 0x7e, 0xb5, 0x20, 0x50, 0xf6, 0x7f, 0x80, 0xa8, 0x61, 0x25, 0x72, 0x25, 0x06, 0x2f, 0x80, 0x90,
    0x01, 0x2f, 0x40, 0xaa, 0x02, 0x2f, 0x00, 0x30, 0x81, 0x05, 0xc2, 0x07, 0xd0, 0x6f, 0x07, 0x0e, 0x49, 0x2f, 0x47,
    0x1a, 0x02, 0x2f, 0xc7, 0x6f, 0x7e, 0x0e, 0x44, 0x2f, 0x00, 0xa9, 0x63, 0x25, 0x74, 0x25, 0x06, 0x2f, 0x00, 0x91,
    0x01, 0x2f, 0xc0, 0xaa, 0x02, 0x2f, 0x07, 0x30, 0xbb, 0x05, 0xfc, 0x07, 0x07, 0x0e, 0x37, 0x2f, 0x47, 0x1a, 0x02,
    0x2f, 0xc7, 0x6f, 0x7e, 0x0e, 0x32, 0x2f, 0xf7, 0x6f, 0xc0, 0xa9, 0x65, 0x25, 0x0b, 0x2f, 0xf7, 0x6f, 0xc0, 0x91,
    0x02, 0x2f, 0x40, 0xab, 0xf7, 0x6f, 0x05, 0x2f, 0xe1, 0x7f, 0x07, 0x30, 0xbd, 0x05, 0xf1, 0x6f, 0xf9, 0x07, 0xe1,
    0x6f, 0x07, 0x0e, 0x20, 0x2f, 0x47, 0x1a, 0x02, 0x2f, 0xc0, 0x6f, 0x46, 0x0e, 0x1b, 0x2f, 0x49, 0x18, 0xd1, 0x18,
    0xd1, 0x18, 0x16, 0x25, 0x07, 0x25, 0x5b, 0x18, 0xe3, 0x18, 0xe3, 0x18, 0xce, 0x00, 0x07, 0x03, 0x6d, 0x18, 0xf2,
    0x6f, 0xd5, 0x18, 0xd5, 0x18, 0x5e, 0x01, 0xe7, 0x02, 0xb4, 0x6f, 0x1c, 0x0e, 0x10, 0x30, 0x08, 0x2f, 0x5c, 0x1a,
    0x02, 0x2f, 0xa3, 0x6f, 0x6b, 0x0e, 0x03, 0x2f, 0x00, 0x2e, 0x02, 0x2c, 0x00, 0x30, 0x00, 0x30, 0xe0, 0x5f, 0xb8,
    0x2e, 0x41, 0x33, 0x81, 0x00, 0x03, 0x30, 0x40, 0x25, 0x02, 0x2c, 0x11, 0x30, 0x13, 0x43, 0x62, 0x1a, 0xfc, 0x2f,
    0x22, 0x33, 0x02, 0x00, 0x00, 0x2e, 0x01, 0x42, 0x80, 0x2e, 0x7e, 0xb5, 0xe1, 0x32, 0x41, 0x00, 0x00, 0x30, 0x40,
    0x42, 0x78, 0x82, 0x00, 0x2e, 0x40, 0x42, 0x43, 0x82, 0x00, 0x2e, 0x50, 0x42, 0x40, 0x42, 0x7d, 0x82, 0x00, 0x2e,
    0x40, 0x42, 0x75, 0x82, 0x00, 0x2e, 0x50, 0x42, 0x40, 0x42, 0x48, 0x82, 0x00, 0x2e, 0x40, 0x42, 0x47, 0x82, 0x00,
    0x2e, 0x50, 0x42, 0x40, 0x42, 0x75, 0x82, 0x00, 0x2e, 0x50, 0x42, 0x40, 0x42, 0xb8, 0x2e, 0x91, 0x50, 0x01, 0x30,
    0x11, 0x42, 0x11, 0x42, 0x11, 0x42, 0x11, 0x42, 0x11, 0x42, 0x01, 0x42, 0xb8, 0x2e, 0x40, 0x18, 0xc8, 0x18, 0xc8,
    0x18, 0xff, 0xbc, 0x61, 0xbb, 0x91, 0x50, 0x71, 0x0a, 0x06, 0x40, 0x71, 0x00, 0x11, 0x42, 0x17, 0x25, 0x52, 0x18,
    0xda, 0x18, 0xda, 0x18, 0x11, 0xb5, 0x03, 0x40, 0x9a, 0x02, 0x12, 0x42, 0x7f, 0xbd, 0xe1, 0xb9, 0xda, 0x0a, 0x27,
    0x25, 0x01, 0x40, 0x64, 0x18, 0xcb, 0x00, 0xec, 0x18, 0x13, 0x42, 0xec, 0x18, 0x21, 0xb6, 0x05, 0x40, 0x2c, 0x03,
    0x14, 0x42, 0x7f, 0xbe, 0xe1, 0xba, 0x2c, 0x0b, 0x05, 0x40, 0x2c, 0x01, 0x14, 0x42, 0x71, 0xb6, 0x05, 0x40, 0x2c,
    0x03, 0x04, 0x42, 0xb8, 0x2e, 0x60, 0x50, 0x91, 0x50, 0xfb, 0x7f, 0xe5, 0x7f, 0xd4, 0x7f, 0x44, 0x36, 0x11, 0x40,
    0x1b, 0x40, 0xc0, 0x7f, 0xbb, 0x7f, 0xa1, 0x7f, 0x05, 0x30, 0x98, 0x2e, 0x79, 0xca, 0x40, 0x18, 0xc5, 0x6f, 0xc8,
    0x18, 0x53, 0x41, 0xc8, 0x18, 0xa2, 0x6f, 0x96, 0x04, 0xc3, 0x7f, 0xa2, 0x7f, 0x44, 0x36, 0xb3, 0x6f, 0x5b, 0x41,
    0x1f, 0x06, 0xd2, 0x6f, 0xe3, 0x6f, 0xd5, 0x7f, 0xe0, 0x7f, 0xbb, 0x7f, 0x05, 0x30, 0x98, 0x2e, 0x79, 0xca, 0x40,
    0x18, 0xc8, 0x18, 0xc8, 0x18, 0xc2, 0x6f, 0xb4, 0x6f, 0x96, 0x04, 0xd3, 0x6f, 0x27, 0x07, 0xa0, 0x6f, 0x02, 0x00,
    0xdb, 0x40, 0xe2, 0x6f, 0xdb, 0x7f, 0x14, 0x03, 0xcb, 0x40, 0xe4, 0x7f, 0xc0, 0x7f, 0xbb, 0x7f, 0x44, 0x36, 0x93,
    0x6f, 0x82, 0x6f, 0x05, 0x30, 0x98, 0x2e, 0x79, 0xca, 0x40, 0x18, 0xc8, 0x18, 0xc8, 0x18, 0xd3, 0x6f, 0xde, 0x04,
    0xb2, 0x6f, 0x97, 0x06, 0xc4, 0x6f, 0x23, 0x00, 0xe1, 0x6f, 0xfb, 0x6f, 0xc0, 0x2e, 0x4a, 0x02, 0xa0, 0x5f, 0x93,
    0x52, 0x00, 0x2e, 0x60, 0x40, 0x41, 0x40, 0x0d, 0xbc, 0x98, 0xbc, 0xc0, 0x2e, 0x01, 0x0a, 0x0f, 0xb8, 0x43, 0x86,
    0x25, 0x40, 0x04, 0x40, 0xd8, 0xbe, 0x2c, 0x0b, 0x22, 0x11, 0x54, 0x42, 0x03, 0x80, 0x4b, 0x0e, 0xf6, 0x2f, 0xb8,
    0x2e, 0x95, 0x50, 0x10, 0x50, 0x97, 0x52, 0x05, 0x2e, 0x8a, 0x00, 0xfb, 0x7f, 0x00, 0x2e, 0x13, 0x40, 0x93, 0x42,
    0x41, 0x0e, 0xfb, 0x2f, 0x98, 0x2e, 0x36, 0xb6, 0x98, 0x2e, 0x87, 0xcf, 0x01, 0x2e, 0x90, 0x00, 0x00, 0xb2, 0x08,
    0x2f, 0x01, 0x2e, 0x69, 0xf7, 0xb1, 0x3f, 0x01, 0x08, 0x01, 0x30, 0x23, 0x2e, 0x90, 0x00, 0x21, 0x2e, 0x69, 0xf7,
    0xfb, 0x6f, 0xf0, 0x5f, 0xb8, 0x2e, 0x10, 0x50, 0xfb, 0x7f, 0x98, 0x2e, 0x56, 0xc7, 0xfb, 0x6f, 0xf0, 0x5f, 0x80,
    0x2e, 0x49, 0xc3, 0x03, 0x2e, 0x8a, 0x00, 0x16, 0xb8, 0x02, 0x34, 0x4a, 0x0c, 0x21, 0x2e, 0x2d, 0xf5, 0xc0, 0x2e,
    0x23, 0x2e, 0x8a, 0x00, 0x21, 0x2e, 0x59, 0xf5, 0x10, 0x30, 0xc0, 0x2e, 0x21, 0x2e, 0x4a, 0xf1, 0x03, 0xbc, 0x21,
    0x2e, 0x8c, 0x00, 0x03, 0x2e, 0x8c, 0x00, 0x40, 0xb2, 0x10, 0x30, 0x21, 0x2e, 0x82, 0x00, 0x01, 0x30, 0x05, 0x2f,
    0x05, 0x2e, 0x8f, 0x00, 0x80, 0x90, 0x01, 0x2f, 0x23, 0x2e, 0x6f, 0xf5, 0xc0, 0x2e, 0x21, 0x2e, 0x90, 0x00, 0x11,
    0x30, 0x81, 0x08, 0x01, 0x2e, 0x6a, 0xf7, 0x71, 0x3f, 0x23, 0xbd, 0x01, 0x08, 0x02, 0x0a, 0xc0, 0x2e, 0x21, 0x2e,
    0x6a, 0xf7, 0x30, 0x25, 0x00, 0x30, 0x21, 0x2e, 0x5a, 0xf5, 0x10, 0x50, 0x21, 0x2e, 0x85, 0x00, 0x21, 0x2e, 0x86,
    0x00, 0xfb, 0x7f, 0x98, 0x2e, 0x5a, 0xb6, 0x40, 0x30, 0x21, 0x2e, 0x8b, 0x00, 0xfb, 0x6f, 0xf0, 0x5f, 0x03, 0x25,
    0x80, 0x2e, 0x46, 0xb6, 0xc0, 0x50, 0x80, 0x7f, 0xe7, 0x7f, 0xd5, 0x7f, 0xc4, 0x7f, 0xb3, 0x7f, 0xa1, 0x7f, 0x92,
    0x7f, 0xf6, 0x7f, 0x7b, 0x7f, 0x00, 0x2e, 0x01, 0x2e, 0x60, 0xf5, 0x60, 0x7f, 0x98, 0x2e, 0xfb, 0xb5, 0x05, 0x2e,
    0xfd, 0xf5, 0x21, 0xb9, 0x11, 0x30, 0x91, 0x08, 0x66, 0x6f, 0x60, 0x7f, 0x52, 0x7f, 0x1b, 0x30, 0x01, 0x30, 0x51,
    0x56, 0x05, 0x30, 0x66, 0x2c, 0x02, 0x32, 0xb3, 0x09, 0x80, 0xb3, 0x4f, 0x2f, 0x00, 0xb2, 0x03, 0x2f, 0x0d, 0x2e,
    0x19, 0x00, 0x80, 0x91, 0x10, 0x2f, 0x0d, 0x2e, 0x91, 0x00, 0x80, 0x91, 0x3d, 0x2f, 0x63, 0x5c, 0x37, 0x2e, 0x91,
    0x00, 0x91, 0x43, 0x91, 0x43, 0x91, 0x43, 0x37, 0x2e, 0x89, 0x00, 0x87, 0x41, 0x87, 0x43, 0x23, 0x2e, 0xe0, 0x00,
    0x31, 0x2d, 0x0b, 0x2e, 0xe0, 0x00, 0x41, 0x8d, 0x2d, 0x2e, 0xe0, 0x00, 0x15, 0x30, 0xb5, 0x09, 0x80, 0xb3, 0x05,
    0x2f, 0x0d, 0x2e, 0x58, 0xf5, 0x68, 0xbf, 0x6e, 0xbb, 0x83, 0x91, 0x21, 0x2f, 0x01, 0x2e, 0xc1, 0xf5, 0x0e, 0xbc,
    0x0e, 0xb8, 0x32, 0x30, 0x90, 0x04, 0xe3, 0x50, 0xe1, 0x52, 0x98, 0x2e, 0x04, 0xb6, 0x50, 0x6f, 0x00, 0xb2, 0x0a,
    0x2f, 0x01, 0x2e, 0xc3, 0xf5, 0x0d, 0xbc, 0x0d, 0xb9, 0xe5, 0x50, 0x41, 0x52, 0x98, 0x2e, 0x04, 0xb6, 0x10, 0x30,
    0x21, 0x2e, 0xe4, 0x00, 0x15, 0x30, 0x2b, 0x2e, 0xdf, 0x00, 0x01, 0x30, 0x23, 0x2e, 0x91, 0x00, 0xb5, 0x25, 0x02,
    0x32, 0x51, 0x56, 0x60, 0x6f, 0x40, 0x91, 0x03, 0x2f, 0x23, 0x2e, 0x96, 0x00, 0x27, 0x2e, 0x64, 0xf5, 0x27, 0x2e,
    0x60, 0xf5, 0x00, 0x90, 0x0d, 0x2f, 0x23, 0x2e, 0x83, 0x00, 0x5f, 0x50, 0x21, 0x2e, 0x5a, 0xf2, 0x45, 0x7f, 0x98,
    0x2e, 0x2e, 0xb6, 0x60, 0x6f, 0x45, 0x6f, 0x02, 0x32, 0x51, 0x56, 0x01, 0x30, 0x1b, 0x30, 0x0d, 0x2e, 0x60, 0xf5,
    0xf2, 0x09, 0xc0, 0x91, 0x97, 0x2f, 0xf3, 0x09, 0xc0, 0x91, 0x94, 0x2f, 0xf6, 0x6f, 0x80, 0x6f, 0x92, 0x6f, 0xa1,
    0x6f, 0xb3, 0x6f, 0xc4, 0x6f, 0xd5, 0x6f, 0xe7, 0x6f, 0x7b, 0x6f, 0x40, 0x5f, 0xc8, 0x2e, 0x80, 0x2e, 0x00, 0xc1,
    0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00,
    0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e,
    0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80,
    0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1,
    0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00,
    0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e,
    0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80,
    0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1,
    0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00,
    0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e,
    0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80,
    0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1,
    0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00,
    0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e,
    0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80,
    0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1,
    0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00,
    0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e,
    0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80,
    0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1,
    0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00,
    0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e,
    0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80,
    0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1,
    0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00,
    0xc1, 0x80, 0x2e, 0x00, 0xc1, 0xfd, 0x2d, 0x01, 0x2e, 0x5d, 0xf7, 0x08, 0xbc, 0x80, 0xac, 0x0e, 0xbb, 0x02, 0x2f,
    0x00, 0x30, 0x41, 0x04, 0x82, 0x06, 0xc0, 0xa4, 0x00, 0x30, 0x11, 0x2f, 0x40, 0xa9, 0x03, 0x2f, 0x40, 0x91, 0x0d,
    0x2f, 0x00, 0xa7, 0x0b, 0x2f, 0x80, 0xb3, 0x99, 0x58, 0x02, 0x2f, 0x90, 0xa1, 0x26, 0x13, 0x20, 0x23, 0x80, 0x90,
    0x10, 0x30, 0x01, 0x2f, 0xcc, 0x0e, 0x00, 0x2f, 0x00, 0x30, 0xb8, 0x2e, 0x9f, 0x56, 0x9b, 0x54, 0xd0, 0x40, 0xc4,
    0x40, 0x0b, 0x2e, 0xfd, 0xf3, 0x9f, 0x52, 0x90, 0x42, 0x94, 0x42, 0x95, 0x42, 0x05, 0x30, 0xa1, 0x50, 0x0f, 0x88,
    0x06, 0x40, 0x04, 0x41, 0x96, 0x42, 0xc5, 0x42, 0x48, 0xbe, 0x73, 0x30, 0x0d, 0x2e, 0x8f, 0x00, 0x4f, 0xba, 0x84,
    0x42, 0x03, 0x42, 0x81, 0xb3, 0x02, 0x2f, 0x2b, 0x2e, 0x6f, 0xf5, 0x06, 0x2d, 0x05, 0x2e, 0x77, 0xf7, 0x69, 0x56,
    0x93, 0x08, 0x25, 0x2e, 0x77, 0xf7, 0x9d, 0x54, 0x25, 0x2e, 0xc2, 0xf5, 0x07, 0x2e, 0xfd, 0xf3, 0x42, 0x30, 0xb4,
    0x33, 0xda, 0x0a, 0x4c, 0x00, 0x27, 0x2e, 0xfd, 0xf3, 0x43, 0x40, 0xd4, 0x3f, 0xdc, 0x08, 0x43, 0x42, 0x00, 0x2e,
    0x00, 0x2e, 0x43, 0x40, 0x24, 0x30, 0xdc, 0x0a, 0x43, 0x42, 0x04, 0x80, 0x03, 0x2e, 0xfd, 0xf3, 0x4a, 0x0a, 0x23,
    0x2e, 0xfd, 0xf3, 0x61, 0x34, 0xc0, 0x2e, 0x01, 0x42, 0x00, 0x2e, 0xd0, 0x51, 0xfb, 0x7f, 0x98, 0x2e, 0xcf, 0x0d,
    0x5a, 0x25, 0x98, 0x2e, 0xf6, 0x0d, 0x6b, 0x87, 0xa9, 0x54, 0xe1, 0x7f, 0xa3, 0x7f, 0xb3, 0x7f, 0xb2, 0x88, 0xa3,
    0x52, 0xc2, 0x7f, 0x65, 0x8b, 0xa5, 0x56, 0x84, 0x7f, 0x61, 0x7f, 0x75, 0x7f, 0xd0, 0x7f, 0x95, 0x7f, 0x53, 0x7f,
    0x14, 0x30, 0xa7, 0x54, 0x81, 0x6f, 0x42, 0x7f, 0x00, 0x2e, 0x53, 0x40, 0x45, 0x8c, 0x42, 0x40, 0x90, 0x41, 0xbb,
    0x83, 0x86, 0x41, 0xd8, 0x04, 0x16, 0x06, 0x00, 0xac, 0x81, 0x7f, 0x02, 0x2f, 0x02, 0x30, 0xd3, 0x04, 0x10, 0x06,
    0xc1, 0x84, 0x01, 0x30, 0xc1, 0x02, 0x0b, 0x16, 0x04, 0x09, 0x14, 0x01, 0x99, 0x02, 0xc1, 0xb9, 0xaf, 0xbc, 0x59,
    0x0a, 0x64, 0x6f, 0x51, 0x43, 0xa1, 0xb4, 0x12, 0x41, 0x13, 0x41, 0x41, 0x43, 0x35, 0x7f, 0x64, 0x7f, 0x26, 0x31,
    0xe5, 0x6f, 0xd4, 0x6f, 0x98, 0x2e, 0x37, 0xca, 0x32, 0x6f, 0x75, 0x6f, 0x83, 0x40, 0x42, 0x41, 0x23, 0x7f, 0x12,
    0x7f, 0xf6, 0x30, 0x40, 0x25, 0x51, 0x25, 0x98, 0x2e, 0x37, 0xca, 0x14, 0x6f, 0x20, 0x05, 0x70, 0x6f, 0x25, 0x6f,
    0x69, 0x07, 0xa2, 0x6f, 0x31, 0x6f, 0x0b, 0x30, 0x04, 0x42, 0x9b, 0x42, 0x8b, 0x42, 0x55, 0x42, 0x32, 0x7f, 0x40,
    0xa9, 0xc3, 0x6f, 0x71, 0x7f, 0x02, 0x30, 0xd0, 0x40, 0xc3, 0x7f, 0x03, 0x2f, 0x40, 0x91, 0x15, 0x2f, 0x00, 0xa7,
    0x13, 0x2f, 0x00, 0xa4, 0x11, 0x2f, 0x84, 0xbd, 0x98, 0x2e, 0x79, 0xca, 0x55, 0x6f, 0xb1, 0x54, 0x54, 0x41, 0x82,
    0x00, 0xf3, 0x3f, 0x45, 0x41, 0xcb, 0x02, 0xf6, 0x30, 0x98, 0x2e, 0x37, 0xca, 0x33, 0x6f, 0xa4, 0x6f, 0xc1, 0x42,
    0x03, 0x2c, 0x00, 0x43, 0xa4, 0x6f, 0x33, 0x6f, 0x00, 0x2e, 0x42, 0x6f, 0x55, 0x6f, 0x91, 0x40, 0x42, 0x8b, 0x00,
    0x41, 0x41, 0x00, 0x01, 0x43, 0x55, 0x7f, 0x14, 0x30, 0xc1, 0x40, 0x95, 0x40, 0x4d, 0x02, 0xc5, 0x6f, 0xaf, 0x50,
    0x68, 0x0e, 0x75, 0x6f, 0xd1, 0x42, 0xa3, 0x7f, 0x8a, 0x2f, 0x09, 0x2e, 0x8f, 0x00, 0x01, 0xb3, 0x22, 0x2f, 0xa9,
    0x58, 0x90, 0x6f, 0x17, 0x30, 0x13, 0x41, 0xb6, 0x6f, 0xe4, 0x7f, 0x00, 0x2e, 0x91, 0x41, 0x14, 0x40, 0x92, 0x41,
    0x15, 0x40, 0x17, 0x2e, 0x6f, 0xf5, 0xb6, 0x7f, 0xd0, 0x7f, 0xcb, 0x7f, 0x98, 0x2e, 0x00, 0x0c, 0x07, 0x15, 0xc2,
    0x6f, 0x14, 0x0b, 0x29, 0x2e, 0x6f, 0xf5, 0xc3, 0xa3, 0xc1, 0x8f, 0xe4, 0x6f, 0xd0, 0x6f, 0xe6, 0x2f, 0x14, 0x30,
    0x05, 0x2e, 0x6f, 0xf5, 0x14, 0x0b, 0x29, 0x2e, 0x6f, 0xf5, 0x44, 0x2d, 0xab, 0x54, 0x01, 0x32, 0xb1, 0x58, 0x05,
    0x30, 0x5f, 0x50, 0x83, 0x40, 0xd8, 0x08, 0x91, 0x01, 0xb8, 0xbd, 0x38, 0xb5, 0xe6, 0x7f, 0x0a, 0x16, 0xb1, 0x6f,
    0x2a, 0xbb, 0xa6, 0xbd, 0x1c, 0x01, 0x06, 0xbc, 0x52, 0x40, 0x06, 0x0a, 0x53, 0x40, 0x45, 0x03, 0xb1, 0x7f, 0xf6,
    0x30, 0x98, 0x2e, 0x37, 0xca, 0x1a, 0xbd, 0x16, 0xb6, 0x86, 0xba, 0x00, 0xa9, 0xaa, 0x0a, 0xb3, 0x52, 0x0f, 0x2f,
    0x00, 0x91, 0xb3, 0x52, 0x03, 0x2f, 0xb3, 0x5a, 0x55, 0x0f, 0xb3, 0x52, 0x08, 0x2f, 0x3f, 0xa1, 0x04, 0x2f, 0x3f,
    0x91, 0x03, 0x2f, 0xb1, 0x58, 0xd4, 0x0f, 0x00, 0x2f, 0xb1, 0x54, 0x12, 0x25, 0xf2, 0x33, 0x98, 0x2e, 0xd9, 0xc0,
    0xe4, 0x6f, 0xf5, 0x37, 0x45, 0x09, 0x21, 0x85, 0x05, 0x43, 0x05, 0x30, 0xad, 0x52, 0x51, 0x0e, 0x01, 0x32, 0xb1,
    0x58, 0xc5, 0x2f, 0x51, 0x54, 0x09, 0x2e, 0x77, 0xf7, 0x22, 0x0b, 0x29, 0x2e, 0x77, 0xf7, 0xfb, 0x6f, 0x30, 0x5e,
    0xb8, 0x2e, 0x10, 0x50, 0x01, 0x2e, 0x8b, 0x00, 0x00, 0xb2, 0xfb, 0x7f, 0x5d, 0x2f, 0x01, 0xb2, 0x54, 0x2f, 0x02,
    0xb2, 0x4e, 0x2f, 0x03, 0x90, 0x63, 0x2f, 0xb9, 0x50, 0x39, 0x82, 0x02, 0x40, 0x81, 0x88, 0xbb, 0x54, 0x41, 0x40,
    0xc1, 0x56, 0x04, 0x42, 0x00, 0x2e, 0x94, 0x40, 0x95, 0x40, 0xd8, 0xbe, 0x2c, 0x0b, 0x45, 0x40, 0x6c, 0x01, 0x55,
    0x42, 0x0c, 0x17, 0x45, 0x40, 0x2c, 0x03, 0x54, 0x42, 0x53, 0x0e, 0xf2, 0x2f, 0xc3, 0x56, 0x3e, 0x82, 0xe2, 0x40,
    0xc3, 0x40, 0x28, 0xbd, 0x93, 0x0a, 0x43, 0x40, 0xda, 0x00, 0x53, 0x42, 0x8a, 0x16, 0x43, 0x40, 0x9a, 0x02, 0x52,
    0x42, 0x00, 0x2e, 0x41, 0x40, 0x51, 0x54, 0x4a, 0x0e, 0x3b, 0x2f, 0x3a, 0x82, 0x00, 0x30, 0x41, 0x40, 0x21, 0x2e,
    0x5e, 0x0f, 0x40, 0xb2, 0x0a, 0x2f, 0x98, 0x2e, 0x61, 0x0c, 0x98, 0x2e, 0x2b, 0x0e, 0x98, 0x2e, 0x41, 0x0e, 0xfb,
    0x6f, 0xf0, 0x5f, 0x00, 0x30, 0x80, 0x2e, 0x65, 0xb6, 0xbf, 0x54, 0xb5, 0x56, 0x83, 0x42, 0x8f, 0x86, 0x74, 0x30,
    0xbd, 0x54, 0xc4, 0x42, 0x11, 0x30, 0x23, 0x2e, 0x8b, 0x00, 0xa1, 0x42, 0x23, 0x30, 0x27, 0x2e, 0x8e, 0x00, 0x21,
    0x2e, 0x8d, 0x00, 0xba, 0x82, 0x18, 0x2c, 0x81, 0x42, 0x30, 0x30, 0x21, 0x2e, 0x8b, 0x00, 0x13, 0x2d, 0x21, 0x30,
    0x00, 0x30, 0x23, 0x2e, 0x8b, 0x00, 0x21, 0x2e, 0x7b, 0xf7, 0x0c, 0x2d, 0x77, 0x30, 0x98, 0x2e, 0x1f, 0x0c, 0xb7,
    0x50, 0x0c, 0x82, 0x12, 0x30, 0x40, 0x42, 0x25, 0x2e, 0x8b, 0x00, 0x2f, 0x2e, 0x7b, 0xf7, 0xfb, 0x6f, 0xf0, 0x5f,
    0xb8, 0x2e, 0x70, 0x50, 0x0a, 0x25, 0x39, 0x86, 0xfb, 0x7f, 0xe1, 0x32, 0x62, 0x30, 0x98, 0x2e, 0xc2, 0xc4, 0x5f,
    0x56, 0xa5, 0x6f, 0xab, 0x08, 0x91, 0x6f, 0x4b, 0x08, 0xc5, 0x56, 0xc4, 0x6f, 0x23, 0x09, 0x4d, 0xba, 0x93, 0xbc,
    0x8c, 0x0b, 0xd1, 0x6f, 0x0b, 0x09, 0xa9, 0x52, 0xc7, 0x5e, 0x56, 0x42, 0xaf, 0x09, 0x4d, 0xba, 0x23, 0xbd, 0x94,
    0x0a, 0xe5, 0x6f, 0x68, 0xbb, 0xeb, 0x08, 0xbd, 0xb9, 0x63, 0xbe, 0xfb, 0x6f, 0x52, 0x42, 0xe3, 0x0a, 0xc0, 0x2e,
    0x43, 0x42, 0x90, 0x5f, 0xaf, 0x50, 0x03, 0x2e, 0x25, 0xf3, 0x12, 0x40, 0x00, 0x40, 0x28, 0xba, 0x9b, 0xbc, 0x88,
    0xbd, 0x93, 0xb4, 0xe3, 0x0a, 0x89, 0x16, 0x08, 0xb6, 0xc0, 0x2e, 0x19, 0x00, 0x62, 0x02, 0x10, 0x50, 0xfb, 0x7f,
    0x98, 0x2e, 0x5d, 0x0d, 0x01, 0x2e, 0x8b, 0x00, 0x31, 0x30, 0x08, 0x04, 0xfb, 0x6f, 0x01, 0x30, 0xf0, 0x5f, 0x23,
    0x2e, 0x8d, 0x00, 0x21, 0x2e, 0x8e, 0x00, 0xb8, 0x2e, 0x01, 0x2e, 0x8e, 0x00, 0x03, 0x2e, 0x8d, 0x00, 0x48, 0x0e,
    0x01, 0x2f, 0x80, 0x2e, 0x05, 0x0e, 0xb8, 0x2e, 0xc9, 0x50, 0x21, 0x34, 0x01, 0x42, 0x82, 0x30, 0xc1, 0x32, 0x25,
    0x2e, 0x62, 0xf5, 0x01, 0x00, 0x22, 0x30, 0x01, 0x40, 0x4a, 0x0a, 0x01, 0x42, 0xb8, 0x2e, 0xc9, 0x54, 0xf0, 0x3b,
    0x83, 0x40, 0xd8, 0x08, 0xcb, 0x52, 0x83, 0x42, 0x00, 0x30, 0x83, 0x30, 0x50, 0x42, 0xc4, 0x32, 0x27, 0x2e, 0x64,
    0xf5, 0x94, 0x00, 0x50, 0x42, 0x40, 0x42, 0xd3, 0x3f, 0x84, 0x40, 0x7d, 0x82, 0xe3, 0x08, 0x40, 0x42, 0x83, 0x42,
    0xb8, 0x2e, 0xbf, 0x52, 0x00, 0x30, 0x40, 0x42, 0x7c, 0x86, 0x9b, 0x52, 0x09, 0x2e, 0x49, 0x0f, 0x9f, 0x54, 0xc4,
    0x42, 0xd3, 0x86, 0x54, 0x40, 0x55, 0x40, 0x94, 0x42, 0x85, 0x42, 0x21, 0x2e, 0x8e, 0x00, 0x42, 0x40, 0x25, 0x2e,
    0xfd, 0xf3, 0xc0, 0x42, 0x7e, 0x82, 0x05, 0x2e, 0x87, 0x00, 0x80, 0xb2, 0x14, 0x2f, 0x05, 0x2e, 0x09, 0x01, 0x27,
    0xbd, 0x2f, 0xb9, 0x80, 0x90, 0x02, 0x2f, 0x21, 0x2e, 0x6f, 0xf5, 0x0c, 0x2d, 0x07, 0x2e, 0x4a, 0x0f, 0x14, 0x30,
    0x1c, 0x09, 0x05, 0x2e, 0x77, 0xf7, 0x69, 0x56, 0x47, 0xbe, 0x93, 0x08, 0x94, 0x0a, 0x25, 0x2e, 0x77, 0xf7, 0xcd,
    0x54, 0x50, 0x42, 0x4a, 0x0e, 0xfc, 0x2f, 0xb8, 0x2e, 0x50, 0x50, 0x02, 0x30, 0x43, 0x86, 0xcb, 0x50, 0xfb, 0x7f,
    0xe3, 0x7f, 0xd2, 0x7f, 0xc0, 0x7f, 0xb1, 0x7f, 0x00, 0x2e, 0x41, 0x40, 0x00, 0x40, 0x48, 0x04, 0x98, 0x2e, 0x74,
    0xc0, 0x1e, 0xaa, 0xd3, 0x6f, 0x14, 0x30, 0xb1, 0x6f, 0xe3, 0x22, 0xc0, 0x6f, 0x52, 0x40, 0xe4, 0x6f, 0x4c, 0x0e,
    0x12, 0x42, 0xd3, 0x7f, 0xeb, 0x2f, 0x03, 0x2e, 0x5f, 0x0f, 0x40, 0x90, 0x11, 0x30, 0x03, 0x2f, 0x23, 0x2e, 0x5f,
    0x0f, 0x02, 0x2c, 0x00, 0x30, 0xd0, 0x6f, 0xfb, 0x6f, 0xb0, 0x5f, 0xb8, 0x2e, 0x40, 0x50, 0xf1, 0x7f, 0x0a, 0x25,
    0x3c, 0x86, 0xeb, 0x7f, 0x41, 0x33, 0x22, 0x30, 0x98, 0x2e, 0xc2, 0xc4, 0xd3, 0x6f, 0xf4, 0x30, 0xdc, 0x09, 0xd1,
    0x58, 0xc2, 0x6f, 0x94, 0x09, 0xd3, 0x58, 0x6a, 0xbb, 0xdc, 0x08, 0xb4, 0xb9, 0xb1, 0xbd, 0xcf, 0x5a, 0x95, 0x08,
    0x21, 0xbd, 0xf6, 0xbf, 0x77, 0x0b, 0x51, 0xbe, 0xf1, 0x6f, 0xeb, 0x6f, 0x52, 0x42, 0x54, 0x42, 0xc0, 0x2e, 0x43,
    0x42, 0xc0, 0x5f, 0x50, 0x50, 0xdd, 0x50, 0x51, 0x30, 0x11, 0x42, 0xfb, 0x7f, 0x7b, 0x30, 0x0b, 0x42, 0x11, 0x30,
    0x02, 0x80, 0x23, 0x33, 0x01, 0x42, 0x03, 0x00, 0x07, 0x2e, 0x80, 0x03, 0x05, 0x2e, 0x8a, 0x00, 0x59, 0x52, 0xe2,
    0x7f, 0xd3, 0x7f, 0xc0, 0x7f, 0x98, 0x2e, 0x9c, 0x0e, 0xd1, 0x6f, 0x08, 0x0a, 0x1a, 0x25, 0x7b, 0x86, 0xd0, 0x7f,
    0x01, 0x33, 0x12, 0x30, 0x98, 0x2e, 0xc2, 0xc4, 0xd1, 0x6f, 0x08, 0x0a, 0x00, 0xb2, 0x0d, 0x2f, 0xe3, 0x6f, 0x01,
    0x2e, 0x80, 0x03, 0x51, 0x30, 0xc7, 0x86, 0x23, 0x2e, 0x21, 0xf2, 0x08, 0xbc, 0xc0, 0x42, 0x98, 0x2e, 0x36, 0xb6,
    0x00, 0x2e, 0x00, 0x2e, 0xd0, 0x2e, 0xb0, 0x6f, 0x0b, 0xb8, 0x03, 0x2e, 0x1b, 0x00, 0x08, 0x1a, 0xb0, 0x7f, 0x70,
    0x30, 0x04, 0x2f, 0x21, 0x2e, 0x21, 0xf2, 0x00, 0x2e, 0x00, 0x2e, 0xd0, 0x2e, 0x98, 0x2e, 0x6d, 0xc0, 0x98, 0x2e,
    0x5d, 0xc0, 0x98, 0x2e, 0x23, 0x03, 0xd5, 0x50, 0x98, 0x2e, 0x44, 0xcb, 0xd7, 0x50, 0x98, 0x2e, 0x46, 0xc3, 0xd9,
    0x50, 0x98, 0x2e, 0x53, 0xc7, 0x20, 0x26, 0xc0, 0x6f, 0x02, 0x31, 0x12, 0x42, 0xab, 0x31, 0x0b, 0x42, 0x37, 0x80,
    0x01, 0x30, 0x01, 0x42, 0x23, 0x33, 0xdf, 0x52, 0xf0, 0x37, 0x44, 0x40, 0xa2, 0x0a, 0x42, 0x42, 0x4b, 0x00, 0x07,
    0x2e, 0x5e, 0xf7, 0x18, 0x08, 0x21, 0x2e, 0xda, 0x00, 0xe1, 0x7f, 0x98, 0x2e, 0x0f, 0xb6, 0xe0, 0x6f, 0x81, 0x30,
    0x01, 0x42, 0x01, 0x30, 0xfb, 0x6f, 0xdb, 0x56, 0x02, 0x30, 0x00, 0x2e, 0x00, 0x2e, 0x81, 0x84, 0x53, 0x0e, 0xfa,
    0x2f, 0x01, 0x42, 0x10, 0x30, 0xb0, 0x5f, 0x21, 0x2e, 0x21, 0xf2, 0xb8, 0x2e, 0xc1, 0x4a, 0x00, 0x00, 0x6d, 0x57,
    0x00, 0x00, 0x77, 0x8e, 0x00, 0x00, 0xdf, 0xff, 0xff, 0xff, 0xd2, 0xff, 0xff, 0xff, 0xe4, 0xff, 0xff, 0xff, 0xed,
    0xe1, 0xff, 0xff, 0x7c, 0x13, 0x00, 0x00, 0x45, 0xe6, 0xff, 0xff, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00,
    0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e,
    0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80,
    0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1,
    0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00,
    0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e,
    0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80,
    0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1,
    0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00,
    0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e,
    0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80,
    0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1,
    0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00,
    0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e,
    0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80,
    0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1,
    0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00, 0xc1, 0x80, 0x2e, 0x00,
    0xc1, 0xfd, 0x2d
};

/*! @name  Global array that stores the feature input configuration of BMI270_DSD */
const struct bmi2_feature_config bmi270_dsd_feat_in[BMI270_DSD_MAX_FEAT_IN] = {
    { .type = BMI2_CONFIG_ID, .page = BMI2_PAGE_1, .start_addr = BMI270_DSD_CONFIG_ID_STRT_ADDR },
    { .type = BMI2_MAX_BURST_LEN, .page = BMI2_PAGE_1, .start_addr = BMI270_DSD_MAX_BURST_LEN_STRT_ADDR },
    { .type = BMI2_CRT_GYRO_SELF_TEST, .page = BMI2_PAGE_1, .start_addr = BMI270_DSD_CRT_GYRO_SELF_TEST_STRT_ADDR },
    { .type = BMI2_ABORT_CRT_GYRO_SELF_TEST, .page = BMI2_PAGE_1, .start_addr = BMI270_DSD_ABORT_STRT_ADDR },
    { .type = BMI2_GYRO_SELF_OFF, .page = BMI2_PAGE_1, .start_addr = BMI270_DSD_GYRO_SELF_OFF_STRT_ADDR },
    { .type = BMI2_NVM_PROG_PREP, .page = BMI2_PAGE_1, .start_addr = BMI270_DSD_NVM_PROG_PREP_STRT_ADDR },
    { .type = BMI2_GYRO_GAIN_UPDATE, .page = BMI2_PAGE_1, .start_addr = BMI270_DSD_GYRO_GAIN_UPDATE_STRT_ADDR },
    { .type = BMI2_DOOR_STATE_DETECTOR, .page = BMI2_PAGE_2, .start_addr = BMI270_DSD_DOOR_STATUS_DET_STRT_ADDR },
    { .type = BMI2_DOOR_STATE_DETECTOR_2, .page = BMI2_PAGE_3, .start_addr = BMI270_DSD_DOOR_STATUS_DET_2_STRT_ADDR },
    { .type = BMI2_ANY_MOTION, .page = BMI2_PAGE_3, .start_addr = BMI270_DSD_ANY_MOT_STRT_ADDR },
    { .type = BMI2_NO_MOTION, .page = BMI2_PAGE_3, .start_addr = BMI270_DSD_NO_MOT_STRT_ADDR }
};

/*! @name  Global array that stores the feature output configuration */
const struct bmi2_feature_config bmi270_dsd_feat_out[BMI270_DSD_MAX_FEAT_OUT] = {
    { .type = BMI270_DSD_DOOR_EVENT_OUTPUT, .page = BMI2_PAGE_0, .start_addr = BMI270_DSD_DOOR_EVENT_OUT_STRT_ADDR },
    { .type = BMI270_DSD_HEADING_OUTPUT, .page = BMI2_PAGE_0, .start_addr = BMI270_DSD_HEADING_OUT_STRT_ADDR },
    { .type = BMI2_GYRO_GAIN_UPDATE, .page = BMI2_PAGE_0, .start_addr = BMI270_DSD_GYR_USER_GAIN_OUT_STRT_ADDR },
    { .type = BMI2_GYRO_CROSS_SENSE, .page = BMI2_PAGE_0, .start_addr = BMI270_DSD_GYRO_CROSS_SENSE_STRT_ADDR },
    { .type = BMI2_NVM_STATUS, .page = BMI2_PAGE_0, .start_addr = BMI270_DSD_NVM_VFRM_OUT_STRT_ADDR },
    { .type = BMI2_VFRM_STATUS, .page = BMI2_PAGE_0, .start_addr = BMI270_DSD_NVM_VFRM_OUT_STRT_ADDR }
};

/*! @name  Global array that stores the feature interrupts of BMI270_DSD */
struct bmi2_map_int bmi270_dsd_map_int[BMI270_DSD_MAX_INT_MAP] = {
    { .type = BMI2_DOOR_STATE_DETECTOR, .sens_map_int = BMI270_DSD_INT_DOOR_STATE_DETECTOR_MASK },
    { .type = BMI2_ANY_MOTION, .sens_map_int = BMI270_DSD_INT_ANY_MOT_MASK },
    { .type = BMI2_NO_MOTION, .sens_map_int = BMI270_DSD_INT_NO_MOT_MASK }
};

/******************************************************************************/

/*!         Local Function Prototypes
 ******************************************************************************/

/*!
 * @brief This internal API is used to validate the device pointer for
 * null conditions.
 *
 * @param[in] dev : Structure instance of bmi2_dev.
 *
 * @return Result of API execution status
 * @retval 0 -> Success
 * @retval < 0 -> Fail
 */
static int8_t null_ptr_check(const struct bmi2_dev *dev);

/*!
 * @brief This internal API enables the selected sensor/features.
 *
 * @param[in]       sensor_sel    : Selects the desired sensor.
 * @param[in, out]  dev           : Structure instance of bmi2_dev.
 *
 * @return Result of API execution status
 * @retval 0 -> Success
 * @retval < 0 -> Fail
 */
static int8_t sensor_enable(uint64_t sensor_sel, struct bmi2_dev *dev);

/*!
 * @brief This internal API disables the selected sensor/features.
 *
 * @param[in]       sensor_sel    : Selects the desired sensor.
 * @param[in, out]  dev           : Structure instance of bmi2_dev.
 *
 * @return Result of API execution status
 * @retval 0 -> Success
 * @retval < 0 -> Fail
 */
static int8_t sensor_disable(uint64_t sensor_sel, struct bmi2_dev *dev);

/*!
 * @brief This internal API selects the sensors/features to be enabled or
 * disabled.
 *
 * @param[in]  sens_list    : Pointer to select the sensor.
 * @param[in]  n_sens       : Number of sensors selected.
 * @param[out] sensor_sel   : Gets the selected sensor.
 *
 * @return Result of API execution status
 * @retval 0 -> Success
 * @retval < 0 -> Fail
 */
static int8_t select_sensor(const uint8_t *sens_list, uint8_t n_sens, uint64_t *sensor_sel);

/*!
 * @brief This internal API gives an option to enable offset correction
 * feature of gyroscope, either internally or by the host.
 *
 * @param[in] enable    : Enables/Disables self-offset correction.
 * @param[in] dev       : Structure instance of bmi2_dev.
 *
 *  enable      |  Description
 * -------------|---------------
 * BMI2_ENABLE  | gyroscope offset correction values are set internally
 * BMI2_DISABLE | gyroscope offset correction values has to be set by host
 *
 * @return Result of API execution status
 * @retval 0 -> Success
 * @retval < 0 -> Fail
 */
static int8_t set_gyro_self_offset_corr(uint8_t enable, struct bmi2_dev *dev);

/*!
 * @brief This internal API is used to enable/disable gyroscope user gain
 * feature.
 *
 * @param[in] dev            : Structure instance of bmi2_dev.
 * @param[in] enable         : Enables/Disables gyroscope user gain.
 *
 * Enable       |  Description
 * -------------|---------------
 * BMI2_DISABLE | Disables gyroscope user gain
 * BMI2_ENABLE  | Enables gyroscope user gain
 *
 * @return Result of API execution status
 * @retval 0 -> Success
 * @retval < 0 -> Fail
 */
static int8_t set_gyro_user_gain(uint8_t enable, struct bmi2_dev *dev);

/*!
 * @brief This internal API gets the error status related to NVM.
 *
 * @param[out] nvm_err_stat     : Stores the NVM error status.
 * @param[in]  dev              : Structure instance of bmi2_dev.
 *
 * @return Result of API execution status
 * @retval 0 -> Success
 * @retval < 0 -> Fail
 */
static int8_t get_nvm_error_status(struct bmi2_nvm_err_status *nvm_err_stat, struct bmi2_dev *dev);

/*!
 * @brief This internal API gets the error status related to virtual frames.
 *
 * @param[out] vfrm_err_stat    : Stores the VFRM related error status.
 * @param[in]  dev              : Structure instance of bmi2_dev.
 *
 * @return Result of API execution status
 * @retval 0 -> Success
 * @retval < 0 -> Fail
 */
static int8_t get_vfrm_error_status(struct bmi2_vfrm_err_status *vfrm_err_stat, struct bmi2_dev *dev);

/*!
 * @brief This internal API is used to get enable status of gyroscope user gain
 * update.
 *
 * @param[out] status         : Stores status of gyroscope user gain update.
 * @param[in]  dev            : Structure instance of bmi2_dev.
 *
 * @return Result of API execution status
 * @retval 0 -> Success
 * @retval < 0 -> Fail
 */
static int8_t get_user_gain_upd_status(uint8_t *status, struct bmi2_dev *dev);

/*!
 * @brief This internal API enables/disables compensation of the gain defined
 * in the GAIN register.
 *
 * @param[in] enable    : Enables/Disables gain compensation
 * @param[in] dev       : Structure instance of bmi2_dev.
 *
 *  enable      |  Description
 * -------------|---------------
 * BMI2_ENABLE  | Enable gain compensation.
 * BMI2_DISABLE | Disable gain compensation.
 *
 * @return Result of API execution status
 * @retval 0 -> Success
 * @retval < 0 -> Fail
 */
static int8_t enable_gyro_gain(uint8_t enable, struct bmi2_dev *dev);

/*!
 * @brief This internal API is used to extract the output feature configuration
 * details like page and start address from the look-up table.
 *
 * @param[out] feat_output      : Structure that stores output feature
 *                              configurations.
 * @param[in] type              : Type of feature or sensor.
 * @param[in] dev               : Structure instance of bmi2_dev.
 *
 * @return Returns the feature found flag.
 *
 * @retval  BMI2_FALSE : Feature not found
 *          BMI2_TRUE  : Feature found
 */
static uint8_t extract_output_feat_config(struct bmi2_feature_config *feat_output,
                                          uint8_t type,
                                          const struct bmi2_dev *dev);

/*!
 * @brief This internal API sets feature configuration to the sensor.
 *
 * @param[in]       sens_cfg     : Structure instance of bmi2_sens_config.
 * @param[in]       loop         : Variable to loop the sensor feature.
 * @param[in, out]  dev          : Structure instance of bmi2_dev.
 *
 * @return Result of API execution status
 * @retval 0 -> Success
 * @retval < 0 -> Fail
 */
static int8_t set_feat_config(const struct bmi2_sens_config *sens_cfg, uint8_t loop, struct bmi2_dev *dev);

/*!
 * @brief This internal API gets feature configuration from the sensor.
 *
 * @param[in]       sens_cfg     : Structure instance of bmi2_sens_config.
 * @param[in]       loop         : Variable to loop the sensor feature.
 * @param[in, out]  dev          : Structure instance of bmi2_dev.
 *
 * @return Result of API execution status
 * @retval 0 -> Success
 * @retval < 0 -> Fail
 */
static int8_t get_feat_config(struct bmi2_sens_config *sens_cfg, uint8_t loop, struct bmi2_dev *dev);

/*!
 * @brief This internal API selects the sensors/features to be enabled or
 * disabled.
 *
 * @param[in]  sens_list    : Pointer to select the sensor.
 * @param[in]  count        : Number of sensors selected.
 * @param[in] sensor_sel    : Gets the selected sensor.
 *
 * @return Result of API execution status
 * @retval 0 -> Success
 * @retval < 0 -> Fail
 */
static int8_t sens_select(const uint8_t *sens_list, uint8_t count, uint64_t *sensor_sel);

/*!
 * @brief This internal API is used to enable main sensors like accel, gyro, aux and temperature.
 *
 * @param[in] sensor_sel    : Enables the selected sensor.
 * @param[in, out]  dev     : Structure instance of bmi2_dev.
 *
 * @return Result of API execution status
 * @retval 0 -> Success
 * @retval < 0 -> Fail
 */
static int8_t enable_main_sensors(uint64_t sensor_sel, struct bmi2_dev *dev);

/*!
 * @brief This internal API is used to enable sensor features.
 *
 * @param[in] sensor_sel    : Enables features of selected sensor.
 * @param[in, out]  dev     : Structure instance of bmi2_dev.
 *
 * @return Result of API execution status
 * @retval 0 -> Success
 * @retval < 0 -> Fail
 */
static int8_t enable_sensor_features(uint64_t sensor_sel, struct bmi2_dev *dev);

/*!
 * @brief This internal API is used to disable main sensors like accel, gyro, aux and temperature.
 *
 * @param[in] sensor_sel    : Disables the selected sensor.
 * @param[in, out]  dev     : Structure instance of bmi2_dev.
 *
 * @return Result of API execution status
 * @retval 0 -> Success
 * @retval < 0 -> Fail
 */
static int8_t disable_main_sensors(uint64_t sensor_sel, struct bmi2_dev *dev);

/*!
 * @brief This internal API is used to disable sensor features.
 *
 * @param[in] sensor_sel    : Disables features of selected sensor.
 * @param[in, out]  dev     : Structure instance of bmi2_dev.
 *
 * @return Result of API execution status
 * @retval 0 -> Success
 * @retval < 0 -> Fail
 */
static int8_t disable_sensor_features(uint64_t sensor_sel, struct bmi2_dev *dev);

/*!
 * @brief This internal API is used to enable/disable door state detector feature.
 *
 * @param[in] dev            : Structure instance of bmi2_dev.
 * @param[in] enable         : Enables/Disables door state detector.
 *
 * Enable       |  Description
 * -------------|---------------
 * BMI2_DISABLE | Disables door state detector.
 * BMI2_ENABLE  | Enables door state detector.
 *
 * @return Result of API execution status
 * @retval 0 -> Success
 * @retval < 0 -> Fail
 */
static int8_t set_door_state_detector(uint8_t enable, struct bmi2_dev *dev);

/*!
 * @brief This internal API sets door state detector configurations like remap setting,
 * calibration threshold and init gyro bias configuration.
 *
 * @param[in]      config      : Structure instance of bmi2_door_state_detector_config.
 * @param[in, out] dev         : Structure instance of bmi2_dev.
 *
 * @verbatim
 *----------------------------------------------------------------------------
 *  bmi2_door_state_detector_config  |
 *  Structure parameters             |                   Description
 *-----------------------------------|--------------------------------------------------
 *                                   | Axis remap status:
 *  remap flag                       | 0: undone, Axis remap needs to be done by the user
 *                                   | 1: done, Axis remap is set in register
 *                                   | Default value is 0.
 * ----------------------------------|---------------------------------------------------
 *                                   | Map the desired axis sign to z axis:
 *  z sign                           | 0: invert, Invert the axis sign
 *                                   | 1: not_invert, Do not invert the axis sign
 *                                   | Default value is 0.
 * ----------------------------------|---------------------------------------------------
 *                                   | Map the desired axis to z axis:
 *                                   | 0：x_axis, Map x axis to z-axis
 *  z axis                           | 1: y_axis, Map y axis to z-axis
 *                                   | 2: z_axis, Map z axis to z-axis
 *                                   | Default value is 0.
 * ----------------------------------|---------------------------------------------------
 *  init_calib_thr                   | Initial calibration threshold.
 *                                   | Default value is 20.
 * ----------------------------------|---------------------------------------------------
 *                                   | Manual reset enable:
 *  reset_enable_flag                | 0: disable.
 *                                   | 1: enable. This bit will be set to 0 after reset is done.
 *                                   | Default value is 0.
 * ----------------------------------|---------------------------------------------------
 *  bias_x_low_word                  |  Value of lower word of bias_x
 * ----------------------------------|---------------------------------------------------
 *  bias_x_high_word                 |  Value of high word of bias_x
 * ----------------------------------|---------------------------------------------------
 *  bias_y_low_word                  |  Value of lower word of bias_y
 * ----------------------------------|---------------------------------------------------
 *  bias_y_high_word                 |  Value of high word of bias_y
 * ----------------------------------|---------------------------------------------------
 *  bias_z_low_word                  |  Value of lower word of bias_z
 * ----------------------------------|---------------------------------------------------
 *  bias_z_high_word                 |  Value of high word of bias_z
 * ----------------------------------|---------------------------------------------------
 * @endverbatim
 *
 * @return Result of API execution status
 * @retval 0 -> Success
 * @retval < 0 -> Fail
 */
static int8_t set_door_state_detector_config_primary(const struct bmi2_door_state_detector_config *config,
                                                     struct bmi2_dev *dev);

/*!
 * @brief This internal API sets door closed threshold.
 *
 * @param[in]      config      : Structure instance of bmi2_door_state_detector_config.
 * @param[in, out] dev         : Structure instance of bmi2_dev.
 *
 * @verbatim
 *----------------------------------------------------------------------------
 *  bmi2_door_state_detector_config  |
 *  Structure parameters             |                   Description
 *-----------------------------------|--------------------------------------------------
 *                                   | Maximum angle when door is closed.
 *  door_closed_thr                  | Default value is 200, resolution: 0.01 deg
 * ----------------------------------|---------------------------------------------------
 * @endverbatim
 *
 * @return Result of API execution status
 * @retval 0 -> Success
 * @retval < 0 -> Fail
 */
static int8_t set_door_closed_thr(const struct bmi2_door_state_detector_config *config, struct bmi2_dev *dev);

/**
 * @brief Sets the common configuration for the door state detector feature.
 *
 * @param[in] config       Pointer to the door state detector configuration structure.
 * @param[in,out] dev      Pointer to the device structure.
 * @param[in] feature_type Type of the feature to configure.
 *
 * @return Status of the operation.
 *         - 0 : Success
 *         - < 0 : Fail
 */
static int8_t set_door_state_detector_config_common(const struct bmi2_door_state_detector_config *config,
                                                    struct bmi2_dev *dev,
                                                    uint8_t feature_type);

/*!
 * @brief his internal API sets door state detector configurations like remap setting,
 * calibration threshold , init gyro bias configuration and door closed threshold.
 *
 * @param[in]      config      : Structure instance of bmi2_door_state_detector_config.
 * @param[in, out] dev         : Structure instance of bmi2_dev.
 *
 *
 * @return Result of API execution status
 * @retval 0 -> Success
 * @retval < 0 -> Fail
 */
static int8_t set_door_state_detector_config(const struct bmi2_door_state_detector_config *config,
                                             struct bmi2_dev *dev);

/*!
 * @brief This internal API gets the outputs of door state detetcor.
 *
 * @param[out] output         :Structure instance of bmi2_door_state_detector_output.
 * @param[in]  dev      : Structure instance of bmi2_dev.
 *
 * door_event_output |  Output
 * ------------------|------------
 * 0x00              |  NONE
 * 0x01              |  DOOR_CLOSE
 * 0x02              |  DOOR_OPEN
 *
 *
 * heading_output     |  Heading output
 *
 * @return Result of API execution status
 * @retval 0 -> Success
 * @retval < 0 -> Fails
 */
static int8_t get_door_state_detector_output(struct bmi2_door_state_detector_output *output, struct bmi2_dev *dev);

/*!
 * @brief This internal API gets door state detector configurations like
 * init remap setting, init gyro threshold and init bias configuration.
 *
 * @param[out]      config    : Structure instance of
 *                              bmi2_door_state_detector_config.
 * @param[in, out]  dev       : Structure instance of bmi2_dev.
 *
 * @return Result of API execution status
 * @retval 0 -> Success
 * @retval < 0 -> Fail
 */
static int8_t get_door_state_detector_config_primary(struct bmi2_door_state_detector_config *config,
                                                     struct bmi2_dev *dev);

/*!
 * @brief This internal API gets door closed threshold.
 *
 * @param[out]      config    : Structure instance of
 *                              bmi2_door_state_detector_config.
 * @param[in, out]  dev       : Structure instance of bmi2_dev.
 *
 * @return Result of API execution status
 * @retval 0 -> Success
 * @retval < 0 -> Fail
 */
static int8_t get_door_closed_thr(struct bmi2_door_state_detector_config *config, struct bmi2_dev *dev);

/**
 * @brief Retrieves the common configuration for the door state detector feature.
 *
 * @param[out] config       Pointer to the structure where the door state detector configuration will be stored.
 * @param[in]  dev          Pointer to the device structure containing device context.
 * @param[in]  feature_type Type of the feature for which the configuration is being retrieved.
 *
 * @return Result of the API execution status.
 *         - 0 : Success
 *         - < 0 : Fail
 */
static int8_t get_door_state_detector_config_common(struct bmi2_door_state_detector_config *config,
                                                    struct bmi2_dev *dev,
                                                    uint8_t feature_type);

/*!
 * @brief This internal API gets door state detector configurations like
 * init remap setting, init gyro threshold, init bias configuration, door closed threshold
 *
 * @param[out]      config    : Structure instance of
 *                              bmi2_door_state_detector_config.
 * @param[in, out]  dev       : Structure instance of bmi2_dev.
 *
 * @return Result of API execution status
 * @retval 0 -> Success
 * @retval < 0 -> Fail
 */
static int8_t get_door_state_detector_config(struct bmi2_door_state_detector_config *config, struct bmi2_dev *dev);

/*!
 * @brief This internal API is used to enable/disable any-motion feature.
 *
 * @param[in] dev            : Structure instance of bmi2_dev.
 * @param[in] enable         : Enables/Disables any-motion.
 *
 * Enable       |  Description
 * -------------|---------------
 * BMI2_DISABLE | Disables any-motion.
 * BMI2_ENABLE  | Enables any-motion.
 *
 * @return Result of API execution status
 * @retval 0 -> Success
 * @retval < 0 -> Fail
 */
static int8_t set_any_motion(uint8_t enable, struct bmi2_dev *dev);

/*!
 * @brief This internal API is used to enable/disable no-motion feature.
 *
 * @param[in] dev            : Structure instance of bmi2_dev.
 * @param[in] enable         : Enables/Disables no-motion.
 *
 * Enable       |  Description
 * -------------|---------------
 * BMI2_DISABLE | Disables no-motion.
 * BMI2_ENABLE  | Enables no-motion.
 *
 * @return Result of API execution status
 * @retval 0 -> Success
 * @retval < 0 -> Fail
 */
static int8_t set_no_motion(uint8_t enable, struct bmi2_dev *dev);

/*!
 * @brief This internal API sets any-motion configurations like axes select,
 * duration, threshold and output-configuration.
 *
 * @param[in]      config      : Structure instance of bmi2_any_motion_config.
 * @param[in, out] dev         : Structure instance of bmi2_dev.
 *
 * @verbatim
 *----------------------------------------------------------------------------
 *  bmi2_any_motion_config  |
 *  Structure parameters    |                   Description
 *--------------------------|--------------------------------------------------
 *                          | Defines the number of  consecutive data points for
 *                          | which the threshold condition must be respected,
 *                          | for interrupt assertion. It is expressed in  50 Hz
 *  duration                | samples (20 msec).
 *                          | Range is 0 to 163sec.
 *                          | Default value is 5 = 100ms.
 * -------------------------|---------------------------------------------------
 *                          | Slope threshold value for in 5.11g format.
 *  threshold               | Range is 0 to 1g.
 *                          | Default value is 0xAA = 83mg.
 * -------------------------|---------------------------------------------------
 *  x_sel, y_sel, z_sel     |  Selects the feature on a per-axis basis
 * -------------------------|---------------------------------------------------
 * @endverbatim
 *
 * @return Result of API execution status
 * @retval 0 -> Success
 * @retval < 0 -> Fail
 */
static int8_t set_any_motion_config(const struct bmi2_any_motion_config *config, struct bmi2_dev *dev);

/*!
 * @brief This internal API sets no-motion configurations like axes select,
 * duration, threshold and output-configuration.
 *
 * @param[in]      config       : Structure instance of bmi2_no_motion_config.
 * @param[in, out] dev          : Structure instance of bmi2_dev.
 *
 * @verbatim
 *----------------------------------------------------------------------------
 *  bmi2_no_motion_config   |
 *  Structure parameters    |                   Description
 *--------------------------|--------------------------------------------------
 *                          | Defines the number of  consecutive data points for
 *                          | which the threshold condition must be respected,
 *                          | for interrupt assertion. It is expressed in  50 Hz
 *  duration                | samples (20 msec).
 *                          | Range is 0 to 163sec.
 *                          | Default value is 5 = 100ms.
 * -------------------------|---------------------------------------------------
 *                          | Slope threshold value for in 5.11g format.
 *  threshold               | Range is 0 to 1g.
 *                          | Default value is 0x90 = 70mg.
 * -------------------------|---------------------------------------------------
 *  x_sel, y_sel, z_sel     |  Selects the feature on a per-axis basis
 * -------------------------|---------------------------------------------------
 * @endverbatim
 *
 * @return Result of API execution status
 * @retval 0 -> Success
 * @retval < 0 -> Fail
 */
static int8_t set_no_motion_config(const struct bmi2_no_motion_config *config, struct bmi2_dev *dev);

/*!
 * @brief This internal API gets any-motion configurations like axes select,
 * duration, threshold and output-configuration.
 *
 * @param[out] config        : Structure instance of bmi2_any_motion_config.
 * @param[in, out]  dev      : Structure instance of bmi2_dev.
 *
 * @verbatim
 *----------------------------------------------------------------------------
 *  bmi2_any_motion_config  |
 *  Structure parameters    |                   Description
 *--------------------------|--------------------------------------------------
 *                          | Defines the number of  consecutive data points for
 *                          | which the threshold condition must be respected,
 *                          | for interrupt assertion. It is expressed in  50 Hz
 *  duration                | samples (20 msec).
 *                          | Range is 0 to 163sec.
 *                          | Default value is 5 = 100ms.
 * -------------------------|---------------------------------------------------
 *                          | Slope threshold value for in 5.11g format.
 *  threshold               | Range is 0 to 1g.
 *                          | Default value is 0xAA = 83mg.
 * -------------------------|---------------------------------------------------
 *  select_x, select_y,     |
 *       select_z           |  Selects the feature on a per-axis basis
 * -------------------------|---------------------------------------------------
 * @endverbatim
 *
 * @return Result of API execution status
 * @retval 0 -> Success
 * @retval < 0 -> Fail
 */
static int8_t get_any_motion_config(struct bmi2_any_motion_config *config, struct bmi2_dev *dev);

/*!
 * @brief This internal API gets no-motion configurations like axes select,
 * duration, threshold and output-configuration.
 *
 * @param[out]      config    : Structure instance of bmi2_no_motion_config.
 * @param[in, out]  dev       : Structure instance of bmi2_dev.
 *
 * @verbatim
 *----------------------------------------------------------------------------
 *  bmi2_no_motion_config   |
 *  Structure parameters    |                   Description
 *--------------------------|--------------------------------------------------
 *                          | Defines the number of  consecutive data points for
 *                          | which the threshold condition must be respected,
 *                          | for interrupt assertion. It is expressed in  50 Hz
 *  duration                | samples (20 msec).
 *                          | Range is 0 to 163sec.
 *                          | Default value is 5 = 100ms.
 * -------------------------|---------------------------------------------------
 *                          | Slope threshold value for in 5.11g format.
 *  threshold               | Range is 0 to 1g.
 *                          | Default value is 0x90 = 70mg.
 * -------------------------|---------------------------------------------------
 *  select_x, select_y,     |
 *       select_z           |  Selects the feature on a per-axis basis
 * -------------------------|---------------------------------------------------
 * @endverbatim
 *
 * @return Result of API execution status
 * @retval 0 -> Success
 * @retval < 0 -> Fail
 */
static int8_t get_no_motion_config(struct bmi2_no_motion_config *config, struct bmi2_dev *dev);

/***************************************************************************/

/*!         User Interface Definitions
 ****************************************************************************/

/*!
 *  @brief This API:
 *  1) Updates the device structure with address of the configuration file.
 *  2) Initializes BMI270_DSD sensor.
 *  3) Writes the configuration file.
 *  4) Updates the feature offset parameters in the device structure.
 *  5) Updates the maximum number of pages, in the device structure.
 */
int8_t bmi270_dsd_init(struct bmi2_dev *dev)
{
    /* Variable to define error */
    int8_t rslt;

    /* Null-pointer check */
    rslt = null_ptr_check(dev);
    if (rslt == BMI2_OK)
    {
        /* Assign chip id of BMI270_DSD */
        dev->chip_id = BMI270_DSD_CHIP_ID;

        /* Get the size of config array */
        dev->config_size = sizeof(bmi270_dsd_config_file);

        /* Enable the variant specific features if any */
        dev->variant_feature = BMI2_CRT_RTOSK_ENABLE | BMI2_GYRO_CROSS_SENS_ENABLE;

        /* An extra dummy byte is read during SPI read */
        if (dev->intf == BMI2_SPI_INTF)
        {
            dev->dummy_byte = 1;
        }
        else
        {
            dev->dummy_byte = 0;
        }

        /* If configuration file pointer is not assigned any address */
        if (!dev->config_file_ptr)
        {
            /* Give the address of the configuration file array to
             * the device pointer
             */
            dev->config_file_ptr = bmi270_dsd_config_file;
        }

        /* Initialize BMI2 sensor */
        rslt = bmi2_sec_init(dev);
        if (rslt == BMI2_OK)
        {
            /* Assign the offsets of the feature input
             * configuration to the device structure
             */
            dev->feat_config = bmi270_dsd_feat_in;

            /* Assign the offsets of the feature output to
             * the device structure
             */
            dev->feat_output = bmi270_dsd_feat_out;

            /* Assign the maximum number of pages to the
             * device structure
             */
            dev->page_max = BMI270_DSD_MAX_PAGE_NUM;

            /* Assign maximum number of input sensors
             * features to device structure
             */
            dev->input_sens = BMI270_DSD_MAX_FEAT_IN;

            /* Assign maximum number of output sensors
             * features to device structure
             */
            dev->out_sens = BMI270_DSD_MAX_FEAT_OUT;

            /* Assign the offsets of the feature interrupt
             * to the device structure
             */
            dev->map_int = bmi270_dsd_map_int;

            /* Assign maximum number of feature interrupts
             * to device structure
             */
            dev->sens_int_map = BMI270_DSD_MAX_INT_MAP;

            /* Get the gyroscope cross axis sensitivity */
            rslt = bmi2_get_gyro_cross_sense(dev);
        }
    }

    return rslt;
}

/*!
 * @brief This API selects the sensors/features to be enabled.
 */
int8_t bmi270_dsd_sensor_enable(const uint8_t *sens_list, uint8_t n_sens, struct bmi2_dev *dev)
{
    /* Variable to define error */
    int8_t rslt;

    /* Variable to select sensor */
    uint64_t sensor_sel = 0;

    /* Null-pointer check */
    rslt = null_ptr_check(dev);
    if ((rslt == BMI2_OK) && (sens_list != NULL))
    {
        /* Get the selected sensors */
        rslt = select_sensor(sens_list, n_sens, &sensor_sel);
        if (rslt == BMI2_OK)
        {
            /* Enable the selected sensors */
            rslt = sensor_enable(sensor_sel, dev);
        }
    }
    else
    {
        rslt = BMI2_E_NULL_PTR;
    }

    return rslt;
}

/*!
 * @brief This API selects the sensors/features to be disabled.
 */
int8_t bmi270_dsd_sensor_disable(const uint8_t *sens_list, uint8_t n_sens, struct bmi2_dev *dev)
{
    /* Variable to define error */
    int8_t rslt;

    /* Variable to select sensor */
    uint64_t sensor_sel = 0;

    /* Null-pointer check */
    rslt = null_ptr_check(dev);
    if ((rslt == BMI2_OK) && (sens_list != NULL))
    {
        /* Get the selected sensors */
        rslt = select_sensor(sens_list, n_sens, &sensor_sel);
        if (rslt == BMI2_OK)
        {
            /* Disable the selected sensors */
            rslt = sensor_disable(sensor_sel, dev);
        }
    }
    else
    {
        rslt = BMI2_E_NULL_PTR;
    }

    return rslt;
}

/*!
 * @brief This API sets the sensor/feature configuration.
 */
int8_t bmi270_dsd_set_sensor_config(struct bmi2_sens_config *sens_cfg, uint8_t n_sens, struct bmi2_dev *dev)
{
    /* Variable to define error */
    int8_t rslt;

    /* Variable to define loop */
    uint8_t loop;

    /* Variable to get the status of advance power save */
    uint8_t aps_stat = 0;

    /* Null-pointer check */
    rslt = null_ptr_check(dev);
    if ((rslt == BMI2_OK) && (sens_cfg != NULL))
    {
        /* Get status of advance power save mode */
        aps_stat = dev->aps_status;

        for (loop = 0; loop < n_sens; loop++)
        {
            if ((sens_cfg[loop].type == BMI2_ACCEL) || (sens_cfg[loop].type == BMI2_GYRO) ||
                (sens_cfg[loop].type == BMI2_AUX) || (sens_cfg[loop].type == BMI2_GYRO_GAIN_UPDATE))
            {
                rslt = bmi2_set_sensor_config(&sens_cfg[loop], 1, dev);
            }
            else
            {
                /* Disable Advance power save if enabled for auxiliary
                 * and feature configurations
                 */
                if (aps_stat == BMI2_ENABLE)
                {
                    /* Disable advance power save if
                     * enabled
                     */
                    rslt = bmi2_set_adv_power_save(BMI2_DISABLE, dev);
                }

                if (rslt == BMI2_OK)
                {
                    rslt = set_feat_config(sens_cfg, loop, dev);
                }
                /* Return error if any of the set configurations fail */
                else
                {
                    break;
                }
            }
        }

        /* Enable Advance power save if disabled while configuring and
         * not when already disabled
         */
        if ((aps_stat == BMI2_ENABLE) && (rslt == BMI2_OK))
        {
            rslt = bmi2_set_adv_power_save(BMI2_ENABLE, dev);
        }
    }
    else
    {
        rslt = BMI2_E_NULL_PTR;
    }

    return rslt;
}

/*!
 * @brief This API gets the sensor/feature configuration.
 */
int8_t bmi270_dsd_get_sensor_config(struct bmi2_sens_config *sens_cfg, uint8_t n_sens, struct bmi2_dev *dev)
{
    /* Variable to define error */
    int8_t rslt;

    /* Variable to define loop */
    uint8_t loop;

    /* Variable to get the status of advance power save */
    uint8_t aps_stat = 0;

    /* Null-pointer check */
    rslt = null_ptr_check(dev);
    if ((rslt == BMI2_OK) && (sens_cfg != NULL))
    {
        /* Get status of advance power save mode */
        aps_stat = dev->aps_status;
        for (loop = 0; loop < n_sens; loop++)
        {
            if ((sens_cfg[loop].type == BMI2_ACCEL) || (sens_cfg[loop].type == BMI2_GYRO) ||
                (sens_cfg[loop].type == BMI2_AUX) || (sens_cfg[loop].type == BMI2_GYRO_GAIN_UPDATE))
            {
                rslt = bmi2_get_sensor_config(&sens_cfg[loop], 1, dev);
            }
            else
            {
                /* Disable Advance power save if enabled for auxiliary
                 * and feature configurations
                 */
                if ((sens_cfg[loop].type >= BMI2_MAIN_SENS_MAX_NUM) || (sens_cfg[loop].type == BMI2_AUX))
                {

                    if (aps_stat == BMI2_ENABLE)
                    {
                        /* Disable advance power save if
                         * enabled
                         */
                        rslt = bmi2_set_adv_power_save(BMI2_DISABLE, dev);
                    }
                }

                if (rslt == BMI2_OK)
                {
                    rslt = get_feat_config(sens_cfg, loop, dev);
                }
                /* Return error if any of the get configurations fail */
                else
                {
                    break;
                }
            }
        }

        /* Enable Advance power save if disabled while configuring and
         * not when already disabled
         */
        if ((aps_stat == BMI2_ENABLE) && (rslt == BMI2_OK))
        {
            rslt = bmi2_set_adv_power_save(BMI2_ENABLE, dev);
        }
    }
    else
    {
        rslt = BMI2_E_NULL_PTR;
    }

    return rslt;
}

/*!
 * @brief This API gets the feature data
 */
int8_t bmi270_dsd_get_feature_data(struct bmi2_feat_sensor_data *feature_data, uint8_t n_sens, struct bmi2_dev *dev)
{
    /* Variable to define error */
    int8_t rslt;

    /* Variable to define loop */
    uint8_t loop;

    /* Variable to get the status of advance power save */
    uint8_t aps_stat = 0;

    /* Null-pointer check */
    rslt = null_ptr_check(dev);
    if ((rslt == BMI2_OK) && (feature_data != NULL))
    {
        /* Get status of advance power save mode */
        aps_stat = dev->aps_status;
        for (loop = 0; loop < n_sens; loop++)
        {
            if ((feature_data[loop].type == BMI2_GYRO_GAIN_UPDATE) ||
                (feature_data[loop].type == BMI2_GYRO_CROSS_SENSE))
            {
                rslt = bmi2_get_feature_data(&feature_data[loop], 1, dev);
            }
            else
            {
                /* Disable Advance power save if enabled for feature
                 * configurations
                 */
                if (feature_data[loop].type >= BMI2_MAIN_SENS_MAX_NUM)
                {
                    if (aps_stat == BMI2_ENABLE)
                    {
                        /* Disable advance power save if
                         * enabled
                         */
                        rslt = bmi2_set_adv_power_save(BMI2_DISABLE, dev);
                    }
                }

                if (rslt == BMI2_OK)
                {
                    switch (feature_data[loop].type)
                    {
                        case BMI2_DOOR_STATE_DETECTOR:

                            /* Get door state detector status  */
                            rslt = get_door_state_detector_output(
                                &feature_data[loop].sens_data.door_state_detector_output,
                                dev);
                            break;

                        case BMI2_NVM_STATUS:

                            /* Get NVM error status  */
                            rslt = get_nvm_error_status(&feature_data[loop].sens_data.nvm_status, dev);
                            break;

                        case BMI2_VFRM_STATUS:

                            /* Get VFRM error status  */
                            rslt = get_vfrm_error_status(&feature_data[loop].sens_data.vfrm_status, dev);
                            break;

                        default:
                            rslt = BMI2_E_INVALID_SENSOR;
                            break;
                    }
                }

                /* Return error if any of the get sensor data fails */
                if (rslt != BMI2_OK)
                {
                    break;
                }
            }

            /* Enable Advance power save if disabled while
             * configuring and not when already disabled
             */
            if ((aps_stat == BMI2_ENABLE) && (rslt == BMI2_OK))
            {
                rslt = bmi2_set_adv_power_save(BMI2_ENABLE, dev);
            }
        }
    }
    else
    {
        rslt = BMI2_E_NULL_PTR;
    }

    return rslt;
}

/*!
 * @brief This API updates the gyroscope user-gain.
 */
int8_t bmi270_dsd_update_gyro_user_gain(const struct bmi2_gyro_user_gain_config *user_gain, struct bmi2_dev *dev)
{
    /* Variable to define error */
    int8_t rslt;

    /* Variable to select sensor */
    uint8_t sens_sel[2] = { BMI2_GYRO, BMI2_GYRO_GAIN_UPDATE };

    /* Structure to define sensor configurations */
    struct bmi2_sens_config sens_cfg;

    /* Variable to store status of user-gain update module */
    uint8_t status = 0;

    /* Variable to define count */
    uint8_t count = 100;

    /* Null-pointer check */
    rslt = null_ptr_check(dev);
    if ((rslt == BMI2_OK) && (user_gain != NULL))
    {
        /* Select type of feature */
        sens_cfg.type = BMI2_GYRO_GAIN_UPDATE;

        /* Get the user gain configurations */
        rslt = bmi270_dsd_get_sensor_config(&sens_cfg, 1, dev);
        if (rslt == BMI2_OK)
        {
            /* Get the user-defined ratio */
            sens_cfg.cfg.gyro_gain_update = *user_gain;

            /* Set rate ratio for all axes */
            rslt = bmi270_dsd_set_sensor_config(&sens_cfg, 1, dev);
        }

        /* Disable gyroscope */
        if (rslt == BMI2_OK)
        {
            rslt = bmi270_dsd_sensor_disable(&sens_sel[0], 1, dev);
        }

        /* Enable gyroscope user-gain update module */
        if (rslt == BMI2_OK)
        {
            rslt = bmi270_dsd_sensor_enable(&sens_sel[1], 1, dev);
        }

        /* Set the command to trigger the computation */
        if (rslt == BMI2_OK)
        {
            rslt = bmi2_set_command_register(BMI2_USR_GAIN_CMD, dev);
        }

        if (rslt == BMI2_OK)
        {
            /* Poll until enable bit of user-gain update is 0 */
            while (count--)
            {
                rslt = get_user_gain_upd_status(&status, dev);
                if ((rslt == BMI2_OK) && (status == 0))
                {
                    /* Enable compensation of gain defined
                     * in the GAIN register
                     */
                    rslt = enable_gyro_gain(BMI2_ENABLE, dev);

                    /* Enable gyroscope */
                    if (rslt == BMI2_OK)
                    {
                        rslt = bmi270_dsd_sensor_enable(&sens_sel[0], 1, dev);
                    }

                    break;
                }

                dev->delay_us(10000, dev->intf_ptr);
            }

            /* Return error if user-gain update is failed */
            if ((rslt == BMI2_OK) && (status != 0))
            {
                rslt = BMI2_E_GYR_USER_GAIN_UPD_FAIL;
            }
        }
    }
    else
    {
        rslt = BMI2_E_NULL_PTR;
    }

    return rslt;
}

/*!
 * @brief This API reads the compensated gyroscope user-gain values.
 */
int8_t bmi270_dsd_read_gyro_user_gain(struct bmi2_gyro_user_gain_data *gyr_usr_gain, struct bmi2_dev *dev)
{
    /* Variable to define error */
    int8_t rslt;

    /* Variable to define register data */
    uint8_t reg_data[3] = { 0 };

    /* Null-pointer check */
    rslt = null_ptr_check(dev);
    if ((rslt == BMI2_OK) && (gyr_usr_gain != NULL))
    {
        /* Get the gyroscope compensated gain values */
        rslt = bmi2_get_regs(BMI2_GYR_USR_GAIN_0_ADDR, reg_data, 3, dev);
        if (rslt == BMI2_OK)
        {
            /* Gyroscope user gain correction X-axis */
            gyr_usr_gain->x = (int8_t)BMI2_GET_BIT_POS0(reg_data[0], BMI2_GYR_USR_GAIN_X);

            /* Gyroscope user gain correction Y-axis */
            gyr_usr_gain->y = (int8_t)BMI2_GET_BIT_POS0(reg_data[1], BMI2_GYR_USR_GAIN_Y);

            /* Gyroscope user gain correction z-axis */
            gyr_usr_gain->z = (int8_t)BMI2_GET_BIT_POS0(reg_data[2], BMI2_GYR_USR_GAIN_Z);
        }
    }
    else
    {
        rslt = BMI2_E_NULL_PTR;
    }

    return rslt;
}

/*!
 * @brief This API maps/unmaps feature interrupts to that of interrupt pins.
 */
int8_t bmi270_dsd_map_feat_int(const struct bmi2_sens_int_config *sens_int, uint8_t n_sens, struct bmi2_dev *dev)
{
    /* Variable to define error */
    int8_t rslt;

    /* Variable to define loop */
    uint8_t loop;

    /* Null-pointer check */
    rslt = null_ptr_check(dev);
    if ((rslt == BMI2_OK) && (sens_int != NULL))
    {
        for (loop = 0; loop < n_sens; loop++)
        {
            switch (sens_int[loop].type)
            {
                case BMI2_DOOR_STATE_DETECTOR:
                case BMI2_ANY_MOTION:
                case BMI2_NO_MOTION:

                    rslt = bmi2_map_feat_int(sens_int[loop].type, sens_int[loop].hw_int_pin, dev);
                    break;
                default:
                    rslt = BMI2_E_INVALID_SENSOR;
                    break;
            }

            /* Return error if interrupt mapping fails */
            if (rslt != BMI2_OK)
            {
                break;
            }
        }
    }
    else
    {
        rslt = BMI2_E_NULL_PTR;
    }

    return rslt;
}

/***************************************************************************/

/*!         Local Function Definitions
 ****************************************************************************/

/*!
 * @brief This internal API is used to validate the device structure pointer for
 * null conditions.
 */
static int8_t null_ptr_check(const struct bmi2_dev *dev)
{
    /* Variable to define error */
    int8_t rslt = BMI2_OK;

    if ((dev == NULL) || (dev->read == NULL) || (dev->write == NULL) || (dev->delay_us == NULL))
    {
        /* Device structure pointer is not valid */
        rslt = BMI2_E_NULL_PTR;
    }

    return rslt;
}

/*!
 * @brief This internal API selects the sensor/features to be enabled or
 * disabled.
 */
static int8_t select_sensor(const uint8_t *sens_list, uint8_t n_sens, uint64_t *sensor_sel)
{
    /* Variable to define error */
    int8_t rslt = BMI2_OK;

    /* Variable to define loop */
    uint8_t count;

    for (count = 0; count < n_sens;)
    {
        if (sens_list[count] == BMI2_ACCEL)
        {
            *sensor_sel |= BMI2_ACCEL_SENS_SEL;
            count++;
        }

        if (sens_list[count] == BMI2_GYRO)
        {
            *sensor_sel |= BMI2_GYRO_SENS_SEL;
            count++;
        }

        if (sens_list[count] == BMI2_AUX)
        {
            *sensor_sel |= BMI2_AUX_SENS_SEL;
            count++;
        }

        if (sens_list[count] == BMI2_TEMP)
        {
            *sensor_sel |= BMI2_TEMP_SENS_SEL;
            count++;
        }

        if (count < n_sens)
        {
            rslt = sens_select(sens_list, count, sensor_sel);
            count++;
        }
    }

    return rslt;
}

/*!
 * @brief This internal API is used to select the sensor feature.
 */
static int8_t sens_select(const uint8_t *sens_list, uint8_t count, uint64_t *sensor_sel)
{
    /* Variable to define error */
    int8_t rslt = BMI2_OK;

    switch (sens_list[count])
    {
        case BMI2_GYRO_GAIN_UPDATE:
            *sensor_sel |= BMI2_GYRO_GAIN_UPDATE_SEL;
            break;
        case BMI2_DOOR_STATE_DETECTOR:
            *sensor_sel |= BMI2_DOOR_STATE_DETECTOR_SEL;
            break;
        case BMI2_GYRO_SELF_OFF:
            *sensor_sel |= BMI2_GYRO_SELF_OFF_SEL;
            break;
        case BMI2_ANY_MOTION:
            *sensor_sel |= BMI2_ANY_MOT_SEL;
            break;
        case BMI2_NO_MOTION:
            *sensor_sel |= BMI2_NO_MOT_SEL;
            break;
        default:
            rslt = BMI2_E_INVALID_SENSOR;
            break;
    }

    return rslt;
}

/*!
 * @brief This internal API enables the selected sensor/features.
 */
static int8_t sensor_enable(uint64_t sensor_sel, struct bmi2_dev *dev)
{
    /* Variable to define error */
    int8_t rslt;

    /* Variable to get the status of advance power save */
    uint8_t aps_stat = 0;

    rslt = enable_main_sensors(sensor_sel, dev);

    if ((rslt == BMI2_OK) && (sensor_sel & ~(BMI2_MAIN_SENSORS)))
    {
        /* Get status of advance power save mode */
        aps_stat = dev->aps_status;
        if (aps_stat == BMI2_ENABLE)
        {
            /* Disable advance power save if enabled */
            rslt = bmi2_set_adv_power_save(BMI2_DISABLE, dev);
        }

        if (rslt == BMI2_OK)
        {
            rslt = enable_sensor_features(sensor_sel, dev);
        }

        /* Enable Advance power save if disabled while
         * configuring and not when already disabled
         */
        if ((aps_stat == BMI2_ENABLE) && (rslt == BMI2_OK))
        {
            rslt = bmi2_set_adv_power_save(BMI2_ENABLE, dev);
        }
    }

    return rslt;
}

/*!
 * @brief This internal API disables the selected sensors/features.
 */
static int8_t sensor_disable(uint64_t sensor_sel, struct bmi2_dev *dev)
{
    /* Variable to define error */
    int8_t rslt;

    /* Variable to get the status of advance power save */
    uint8_t aps_stat = 0;

    rslt = disable_main_sensors(sensor_sel, dev);

    if ((rslt == BMI2_OK) && (sensor_sel & ~(BMI2_MAIN_SENSORS)))
    {
        /* Get status of advance power save mode */
        aps_stat = dev->aps_status;
        if (aps_stat == BMI2_ENABLE)
        {
            /* Disable advance power save if enabled */
            rslt = bmi2_set_adv_power_save(BMI2_DISABLE, dev);
        }

        if (rslt == BMI2_OK)
        {
            rslt = disable_sensor_features(sensor_sel, dev);

            /* Enable Advance power save if disabled while
             * configuring and not when already disabled
             */
            if ((aps_stat == BMI2_ENABLE) && (rslt == BMI2_OK))
            {
                rslt = bmi2_set_adv_power_save(BMI2_ENABLE, dev);
            }
        }
    }

    return rslt;
}

/*!
 * @brief This internal API is used to enable/disable any motion feature.
 */
static int8_t set_any_motion(uint8_t enable, struct bmi2_dev *dev)
{
    /* Variable to define error */
    int8_t rslt;

    /* Array to define the feature configuration */
    uint8_t feat_config[BMI2_FEAT_SIZE_IN_BYTES] = { 0 };

    /* Variable to define the array offset */
    uint8_t idx = 0;

    /* Variable to set flag */
    uint8_t feat_found;

    /* Initialize feature configuration for any-motion */
    struct bmi2_feature_config any_mot_config = { 0, 0, 0 };

    /* Search for any-motion feature and extract its configurations details */
    feat_found = bmi2_extract_input_feat_config(&any_mot_config, BMI2_ANY_MOTION, dev);
    if (feat_found)
    {
        /* Get the configuration from the page where any-motion feature resides */
        rslt = bmi2_get_feat_config(any_mot_config.page, feat_config, dev);
        if (rslt == BMI2_OK)
        {
            /* Define the offset for enable/disable of any-motion axes */
            idx = any_mot_config.start_addr + BMI2_ANY_MOT_FEAT_EN_OFFSET;

            /* Set the feature enable bit */
            feat_config[idx] = BMI2_SET_BITS(feat_config[idx], BMI2_ANY_NO_MOT_EN, enable);

            /* Set the configuration back to the page */
            rslt = bmi2_set_regs(BMI2_FEATURES_REG_ADDR, feat_config, BMI2_FEAT_SIZE_IN_BYTES, dev);

            if ((rslt == BMI2_OK) && (enable == BMI2_ENABLE))
            {
                dev->sens_en_stat |= BMI2_ANY_MOT_SEL;
            }
            else
            {
                dev->sens_en_stat &= ~BMI2_ANY_MOT_SEL;
            }
        }
    }
    else
    {
        rslt = BMI2_E_INVALID_SENSOR;
    }

    return rslt;
}

/*!
 * @brief This internal API is used to enable/disable no-motion feature.
 */
static int8_t set_no_motion(uint8_t enable, struct bmi2_dev *dev)
{
    /* Variable to define error */
    int8_t rslt;

    /* Array to define the feature configuration */
    uint8_t feat_config[BMI2_FEAT_SIZE_IN_BYTES] = { 0 };

    /* Variable to define the array offset */
    uint8_t idx = 0;

    /* Variable to set flag */
    uint8_t feat_found;

    /* Initialize feature configuration for no-motion */
    struct bmi2_feature_config no_mot_config = { 0, 0, 0 };

    /* Search for no-motion feature and extract its configurations details */
    feat_found = bmi2_extract_input_feat_config(&no_mot_config, BMI2_NO_MOTION, dev);
    if (feat_found)
    {
        /* Get the configuration from the page where any/no-motion feature resides */
        rslt = bmi2_get_feat_config(no_mot_config.page, feat_config, dev);
        if (rslt == BMI2_OK)
        {
            /* Define the offset for enable/disable of no-motion axes */
            idx = no_mot_config.start_addr + BMI2_NO_MOT_FEAT_EN_OFFSET;

            /* Set the feature enable bit */
            feat_config[idx] = BMI2_SET_BITS(feat_config[idx], BMI2_ANY_NO_MOT_EN, enable);

            /* Set the configuration back to the page */
            rslt = bmi2_set_regs(BMI2_FEATURES_REG_ADDR, feat_config, BMI2_FEAT_SIZE_IN_BYTES, dev);

            if ((rslt == BMI2_OK) && (enable == BMI2_ENABLE))
            {
                dev->sens_en_stat |= BMI2_NO_MOT_SEL;
            }
            else
            {
                dev->sens_en_stat &= ~BMI2_NO_MOT_SEL;
            }
        }
    }
    else
    {
        rslt = BMI2_E_INVALID_SENSOR;
    }

    return rslt;
}

/*!
 * @brief This internal API is used to enable/disable door state detector feature.
 */
static int8_t set_door_state_detector(uint8_t enable, struct bmi2_dev *dev)
{
    /* Variable to define error */
    int8_t rslt;

    /* Array to define the feature configuration */
    uint8_t feat_config[BMI2_FEAT_SIZE_IN_BYTES] = { 0 };

    /* Variable to define the array offset */
    uint8_t idx = 0;

    /* Variable to set flag */
    uint8_t feat_found;

    /* Initialize feature configuration for door state detector */
    struct bmi2_feature_config dsd_config = { 0, 0, 0 };

    /* Search for door state detector feature and extract its configurations details */
    feat_found = bmi2_extract_input_feat_config(&dsd_config, BMI2_DOOR_STATE_DETECTOR, dev); /* 1 */
    if (feat_found)
    {
        /* Get the configuration from the page where door state detector feature resides */
        rslt = bmi2_get_feat_config(dsd_config.page, feat_config, dev);
        if (rslt == BMI2_OK)
        {
            /* Define the offset for enable/disable of any-motion axes */
            idx = dsd_config.start_addr;

            /* Set the feature enable bit */
            feat_config[idx] = BMI2_SET_BIT_POS0(feat_config[idx], BMI2_DOOR_STATE_DETECTOR_FEAT_EN, enable);

            /* Set the configuration back to the page */
            rslt = bmi2_set_regs(BMI2_FEATURES_REG_ADDR, feat_config, BMI2_FEAT_SIZE_IN_BYTES, dev);
        }
    }
    else
    {
        rslt = BMI2_E_INVALID_SENSOR;
    }

    return rslt;
}

/*!
 * @brief This internal API gives an option to enable self-offset correction
 * feature of gyroscope, either internally or by the host.
 */
static int8_t set_gyro_self_offset_corr(uint8_t enable, struct bmi2_dev *dev)
{
    /* Variable to define error */
    int8_t rslt;

    /* Array to define the feature configuration */
    uint8_t feat_config[BMI2_FEAT_SIZE_IN_BYTES] = { 0 };

    /* Variable to define the array offset */
    uint8_t idx = 0;

    /* Variable to set flag */
    uint8_t feat_found;

    /* Initialize feature configuration for self-offset correction */
    struct bmi2_feature_config self_off_corr_cfg = { 0, 0, 0 };

    /* Search for self-offset correction and extract its configuration details */
    feat_found = bmi2_extract_input_feat_config(&self_off_corr_cfg, BMI2_GYRO_SELF_OFF, dev);
    if (feat_found)
    {
        /* Get the configuration from the page where self-offset
         * correction feature resides
         */
        rslt = bmi2_get_feat_config(self_off_corr_cfg.page, feat_config, dev);
        if (rslt == BMI2_OK)
        {
            /* Define the offset for enable/disable of self-offset correction */
            idx = self_off_corr_cfg.start_addr;

            /* Set the feature enable bit */
            feat_config[idx] = BMI2_SET_BITS(feat_config[idx], BMI2_GYR_SELF_OFF_CORR_FEAT_EN, enable);

            /* Set the configuration back to the page */
            rslt = bmi2_set_regs(BMI2_FEATURES_REG_ADDR, feat_config, BMI2_FEAT_SIZE_IN_BYTES, dev);
        }
    }
    else
    {
        rslt = BMI2_E_INVALID_SENSOR;
    }

    return rslt;
}

/*!
 * @brief This internal API is used to enable/disable gyroscope user gain
 * feature.
 */
static int8_t set_gyro_user_gain(uint8_t enable, struct bmi2_dev *dev)
{
    /* Variable to define error */
    int8_t rslt;

    /* Array to define the feature configuration */
    uint8_t feat_config[BMI2_FEAT_SIZE_IN_BYTES] = { 0 };

    /* Variable to define the array offset */
    uint8_t idx = 0;

    /* Variable to set flag */
    uint8_t feat_found;

    /* Initialize feature configuration for gyroscope user gain */
    struct bmi2_feature_config gyr_user_gain_cfg = { 0, 0, 0 };

    /* Search for user gain feature and extract its configuration details */
    feat_found = bmi2_extract_input_feat_config(&gyr_user_gain_cfg, BMI2_GYRO_GAIN_UPDATE, dev);
    if (feat_found)
    {
        /* Get the configuration from the page where user gain feature resides */
        rslt = bmi2_get_feat_config(gyr_user_gain_cfg.page, feat_config, dev);
        if (rslt == BMI2_OK)
        {
            /* Define the offset for enable/disable of user gain */
            idx = gyr_user_gain_cfg.start_addr + BMI2_GYR_USER_GAIN_FEAT_EN_OFFSET;

            /* Set the feature enable bit */
            feat_config[idx] = BMI2_SET_BITS(feat_config[idx], BMI2_GYR_USER_GAIN_FEAT_EN, enable);

            /* Set the configuration back to the page */
            rslt = bmi2_set_regs(BMI2_FEATURES_REG_ADDR, feat_config, BMI2_FEAT_SIZE_IN_BYTES, dev);
        }
    }
    else
    {
        rslt = BMI2_E_INVALID_SENSOR;
    }

    return rslt;
}

/*!
 * @brief This internal API gets the output values of door state detector.
 */
static int8_t get_door_state_detector_output(struct bmi2_door_state_detector_output *output, struct bmi2_dev *dev)
{
    /* Variable to define error */
    int8_t rslt;

    /* Array to define the feature configuration */
    uint8_t feat_config[BMI2_FEAT_SIZE_IN_BYTES] = { 0 };

    /* Variables to define index */
    uint8_t idx = 0;

    /* Variable to define LSB */
    uint16_t lsb;

    /* Variable to define MSB */
    uint16_t msb;

    /* Variable to set flag */
    uint8_t door_event_found, heading_value_found;

    /* Initialize feature output */
    struct bmi2_feature_config door_event_output = { 0, 0, 0 };
    struct bmi2_feature_config heading_value_output = { 0, 0, 0 };

    /* Search for door event output feature and extract its configuration details */
    door_event_found = extract_output_feat_config(&door_event_output, BMI270_DSD_DOOR_EVENT_OUTPUT, dev);
    heading_value_found = extract_output_feat_config(&heading_value_output, BMI270_DSD_HEADING_OUTPUT, dev);
    if (door_event_found && heading_value_found)
    {
        /* Get the feature output configuration for door event */
        rslt = bmi2_get_feat_config(door_event_output.page, feat_config, dev);
        if (rslt == BMI2_OK)
        {
            /* Define the offset in bytes for door event output */
            idx = door_event_output.start_addr;

            /* Get the door event output */
            output->door_event_output = feat_config[idx];

            /* Define the offset in bytes for heading output */
            idx = heading_value_output.start_addr;

            /* Get word to calculate init_calib_thr, reset_enable_flag configuration from the same word */
            lsb = (uint16_t)feat_config[idx++];
            msb = ((uint16_t)feat_config[idx++] << 8);
            output->heading_output = (int16_t)(lsb | msb);
        }
    }
    else
    {
        rslt = BMI2_E_INVALID_SENSOR;
    }

    return rslt;
}

/*!
 * @brief This internal API gets the error status related to NVM.
 */
static int8_t get_nvm_error_status(struct bmi2_nvm_err_status *nvm_err_stat, struct bmi2_dev *dev)
{
    /* Variable to define error */
    int8_t rslt;

    /* Array to define the feature configuration */
    uint8_t feat_config[BMI2_FEAT_SIZE_IN_BYTES] = { 0 };

    /* Variables to define index */
    uint8_t idx = 0;

    /* Variable to set flag */
    uint8_t feat_found;

    /* Initialize feature output for NVM error status */
    struct bmi2_feature_config nvm_err_cfg = { 0, 0, 0 };

    /* Search for NVM error status feature and extract its configuration details */
    feat_found = extract_output_feat_config(&nvm_err_cfg, BMI2_NVM_STATUS, dev);
    if (feat_found)
    {
        /* Get the feature output configuration for NVM error status */
        rslt = bmi2_get_feat_config(nvm_err_cfg.page, feat_config, dev);
        if (rslt == BMI2_OK)
        {
            /* Define the offset in bytes for NVM error status */
            idx = nvm_err_cfg.start_addr;

            /* Increment index to get the error status */
            idx++;

            /* Error when NVM load action fails */
            nvm_err_stat->load_error = BMI2_GET_BIT_POS0(feat_config[idx], BMI2_NVM_LOAD_ERR_STATUS);

            /* Error when NVM program action fails */
            nvm_err_stat->prog_error = BMI2_GET_BITS(feat_config[idx], BMI2_NVM_PROG_ERR_STATUS);

            /* Error when NVM erase action fails */
            nvm_err_stat->erase_error = BMI2_GET_BITS(feat_config[idx], BMI2_NVM_ERASE_ERR_STATUS);

            /* Error when NVM program limit is exceeded */
            nvm_err_stat->exceed_error = BMI2_GET_BITS(feat_config[idx], BMI2_NVM_END_EXCEED_STATUS);

            /* Error when NVM privilege mode is not acquired */
            nvm_err_stat->privil_error = BMI2_GET_BITS(feat_config[idx], BMI2_NVM_PRIV_ERR_STATUS);
        }
    }
    else
    {
        rslt = BMI2_E_INVALID_SENSOR;
    }

    return rslt;
}

/*!
 * @brief This internal API is used to get enable status of gyroscope user gain
 * update.
 */
static int8_t get_user_gain_upd_status(uint8_t *status, struct bmi2_dev *dev)
{
    /* Variable to define error */
    int8_t rslt = BMI2_OK;

    /* Array to define the feature configuration */
    uint8_t feat_config[BMI2_FEAT_SIZE_IN_BYTES] = { 0 };

    /* Variable to define the array offset */
    uint8_t idx = 0;

    /* Variable to set flag */
    uint8_t feat_found;

    /* Variable to check APS status */
    uint8_t aps_stat = 0;

    /* Initialize feature configuration for gyroscope user gain */
    struct bmi2_feature_config gyr_user_gain_cfg = { 0, 0, 0 };

    /* Search for user gain feature and extract its configuration details */
    feat_found = bmi2_extract_input_feat_config(&gyr_user_gain_cfg, BMI2_GYRO_GAIN_UPDATE, dev);
    if (feat_found)
    {
        /* Disable advance power save */
        aps_stat = dev->aps_status;
        if (aps_stat == BMI2_ENABLE)
        {
            rslt = bmi2_set_adv_power_save(BMI2_DISABLE, dev);
        }

        if (rslt == BMI2_OK)
        {
            /* Get the configuration from the page where user gain feature resides */
            rslt = bmi2_get_feat_config(gyr_user_gain_cfg.page, feat_config, dev);
            if (rslt == BMI2_OK)
            {
                /* Define the offset for enable/disable of user gain */
                idx = gyr_user_gain_cfg.start_addr + BMI2_GYR_USER_GAIN_FEAT_EN_OFFSET;

                /* Set the feature enable status */
                *status = BMI2_GET_BITS(feat_config[idx], BMI2_GYR_USER_GAIN_FEAT_EN);
            }
        }
    }
    else
    {
        rslt = BMI2_E_INVALID_SENSOR;
    }

    /* Enable Advance power save if disabled while configuring and not when already disabled */
    if ((rslt == BMI2_OK) && (aps_stat == BMI2_ENABLE))
    {
        rslt = bmi2_set_adv_power_save(BMI2_ENABLE, dev);
    }

    return rslt;
}

/*!
 * @brief This internal API enables/disables compensation of the gain defined
 * in the GAIN register.
 */
static int8_t enable_gyro_gain(uint8_t enable, struct bmi2_dev *dev)
{
    /* Variable to define error */
    int8_t rslt;

    /* Variable to define register data */
    uint8_t reg_data = 0;

    rslt = bmi2_get_regs(BMI2_GYR_OFF_COMP_6_ADDR, &reg_data, 1, dev);
    if (rslt == BMI2_OK)
    {
        reg_data = BMI2_SET_BITS(reg_data, BMI2_GYR_GAIN_EN, enable);
        rslt = bmi2_set_regs(BMI2_GYR_OFF_COMP_6_ADDR, &reg_data, 1, dev);
    }

    return rslt;
}

/*!
 * @brief This internal API sets any-motion configurations like axes select,
 * duration, threshold and output-configuration.
 */
static int8_t set_any_motion_config(const struct bmi2_any_motion_config *config, struct bmi2_dev *dev)
{
    /* Variable to define error */
    int8_t rslt;

    /* Array to define the feature configuration */
    uint8_t feat_config[BMI2_FEAT_SIZE_IN_BYTES] = { 0 };

    /* Variable to define the array offset */
    uint8_t idx = 0;

    /* Variable to define count */
    uint8_t index = 0;

    /* Variable to set flag */
    uint8_t feat_found;

    /* Initialize feature configuration for any motion */
    struct bmi2_feature_config any_mot_config = { 0, 0, 0 };

    /* Copy the feature configuration address to a local pointer */
    uint16_t *data_p = (uint16_t *) (void *)feat_config;

    /* Search for any-motion feature and extract its configuration details */
    feat_found = bmi2_extract_input_feat_config(&any_mot_config, BMI2_ANY_MOTION, dev);
    if (feat_found)
    {
        /* Get the configuration from the page where any-motion feature resides */
        rslt = bmi2_get_feat_config(any_mot_config.page, feat_config, dev);
        if (rslt == BMI2_OK)
        {
            /* Define the offset in bytes for any-motion select */
            idx = any_mot_config.start_addr;

            /* Get offset in words since all the features are set in words length */
            idx = idx / 2;

            /* Set duration */
            *(data_p + idx) = BMI2_SET_BIT_POS0(*(data_p + idx), BMI2_ANY_NO_MOT_DUR, config->duration);

            /* Set x-select */
            *(data_p + idx) = BMI2_SET_BITS(*(data_p + idx), BMI2_ANY_NO_MOT_X_SEL, config->select_x);

            /* Set y-select */
            *(data_p + idx) = BMI2_SET_BITS(*(data_p + idx), BMI2_ANY_NO_MOT_Y_SEL, config->select_y);

            /* Set z-select */
            *(data_p + idx) = BMI2_SET_BITS(*(data_p + idx), BMI2_ANY_NO_MOT_Z_SEL, config->select_z);

            /* Increment offset by 1 word to set threshold and output configuration */
            idx++;

            /* Set threshold */
            *(data_p + idx) = BMI2_SET_BIT_POS0(*(data_p + idx), BMI2_ANY_NO_MOT_THRES, config->threshold);

            /* Increment offset by 1 more word to get the total length in words */
            idx++;

            /* Get total length in bytes to copy from local pointer to the array */
            idx = (uint8_t)(idx * 2) - any_mot_config.start_addr;

            /* Copy the bytes to be set back to the array */
            for (index = 0; index < idx; index++)
            {
                feat_config[any_mot_config.start_addr +
                            index] = *((uint8_t *) data_p + any_mot_config.start_addr + index);
            }

            /* Set the configuration back to the page */
            rslt = bmi2_set_regs(BMI2_FEATURES_REG_ADDR, feat_config, BMI2_FEAT_SIZE_IN_BYTES, dev);
        }
    }
    else
    {
        rslt = BMI2_E_INVALID_SENSOR;
    }

    return rslt;
}

/*!
 * @brief This internal API sets no-motion configurations like axes select,
 * duration, threshold and output-configuration.
 */
static int8_t set_no_motion_config(const struct bmi2_no_motion_config *config, struct bmi2_dev *dev)
{
    /* Variable to define error */
    int8_t rslt;

    /* Array to define the feature configuration */
    uint8_t feat_config[BMI2_FEAT_SIZE_IN_BYTES] = { 0 };

    /* Variable to define the array offset */
    uint8_t idx = 0;

    /* Variable to define count */
    uint8_t index = 0;

    /* Variable to set flag */
    uint8_t feat_found;

    /* Initialize feature configuration for no-motion */
    struct bmi2_feature_config no_mot_config = { 0, 0, 0 };

    /* Copy the feature configuration address to a local pointer */
    uint16_t *data_p = (uint16_t *) (void *)feat_config;

    /* Search for no-motion feature and extract its configuration details */
    feat_found = bmi2_extract_input_feat_config(&no_mot_config, BMI2_NO_MOTION, dev);
    if (feat_found)
    {
        /* Get the configuration from the page where no-motion feature resides */
        rslt = bmi2_get_feat_config(no_mot_config.page, feat_config, dev);
        if (rslt == BMI2_OK)
        {
            /* Define the offset in bytes for no-motion select */
            idx = no_mot_config.start_addr;

            /* Get offset in words since all the features are set in words length */
            idx = idx / 2;

            /* Set duration */
            *(data_p + idx) = BMI2_SET_BIT_POS0(*(data_p + idx), BMI2_ANY_NO_MOT_DUR, config->duration);

            /* Set x-select */
            *(data_p + idx) = BMI2_SET_BITS(*(data_p + idx), BMI2_ANY_NO_MOT_X_SEL, config->select_x);

            /* Set y-select */
            *(data_p + idx) = BMI2_SET_BITS(*(data_p + idx), BMI2_ANY_NO_MOT_Y_SEL, config->select_y);

            /* Set z-select */
            *(data_p + idx) = BMI2_SET_BITS(*(data_p + idx), BMI2_ANY_NO_MOT_Z_SEL, config->select_z);

            /* Increment offset by 1 word to set threshold and output configuration */
            idx++;

            /* Set threshold */
            *(data_p + idx) = BMI2_SET_BIT_POS0(*(data_p + idx), BMI2_ANY_NO_MOT_THRES, config->threshold);

            /* Increment offset by 1 more word to get the total length in words */
            idx++;

            /* Get total length in bytes to copy from local pointer to the array */
            idx = (uint8_t)(idx * 2) - no_mot_config.start_addr;

            /* Copy the bytes to be set back to the array */
            for (index = 0; index < idx; index++)
            {
                feat_config[no_mot_config.start_addr +
                            index] = *((uint8_t *) data_p + no_mot_config.start_addr + index);
            }

            /* Set the configuration back to the page */
            rslt = bmi2_set_regs(BMI2_FEATURES_REG_ADDR, feat_config, BMI2_FEAT_SIZE_IN_BYTES, dev);
        }
    }
    else
    {
        rslt = BMI2_E_INVALID_SENSOR;
    }

    return rslt;
}

/*!
 * @brief This internal API gets any-motion configurations like axes select,
 * duration, threshold and output-configuration.
 */
static int8_t get_any_motion_config(struct bmi2_any_motion_config *config, struct bmi2_dev *dev)
{
    /* Variable to define error */
    int8_t rslt;

    /* Array to define the feature configuration */
    uint8_t feat_config[BMI2_FEAT_SIZE_IN_BYTES] = { 0 };

    /* Variable to define the array offset */
    uint8_t idx = 0;

    /* Variable to define LSB */
    uint16_t lsb;

    /* Variable to define MSB */
    uint16_t msb;

    /* Variable to define a word */
    uint16_t lsb_msb;

    /* Variable to set flag */
    uint8_t feat_found;

    /* Initialize feature configuration for any-motion */
    struct bmi2_feature_config any_mot_config = { 0, 0, 0 };

    /* Search for any-motion feature and extract its configuration details */
    feat_found = bmi2_extract_input_feat_config(&any_mot_config, BMI2_ANY_MOTION, dev);
    if (feat_found)
    {
        /* Get the configuration from the page where any-motion feature resides */
        rslt = bmi2_get_feat_config(any_mot_config.page, feat_config, dev);
        if (rslt == BMI2_OK)
        {
            /* Define the offset for feature enable for any-motion */
            idx = any_mot_config.start_addr;

            /* Get word to calculate duration, x, y and z select */
            lsb = (uint16_t) feat_config[idx++];
            msb = ((uint16_t) feat_config[idx++] << 8);
            lsb_msb = lsb | msb;

            /* Get duration */
            config->duration = lsb_msb & BMI2_ANY_NO_MOT_DUR_MASK;

            /* Get x-select */
            config->select_x = (lsb_msb & BMI2_ANY_NO_MOT_X_SEL_MASK) >> BMI2_ANY_NO_MOT_X_SEL_POS;

            /* Get y-select */
            config->select_y = (lsb_msb & BMI2_ANY_NO_MOT_Y_SEL_MASK) >> BMI2_ANY_NO_MOT_Y_SEL_POS;

            /* Get z-select */
            config->select_z = (lsb_msb & BMI2_ANY_NO_MOT_Z_SEL_MASK) >> BMI2_ANY_NO_MOT_Z_SEL_POS;

            /* Get word to calculate threshold, output configuration from the same word */
            lsb = (uint16_t) feat_config[idx++];
            msb = ((uint16_t) feat_config[idx++] << 8);
            lsb_msb = lsb | msb;

            /* Get threshold */
            config->threshold = lsb_msb & BMI2_ANY_NO_MOT_THRES_MASK;
        }
    }
    else
    {
        rslt = BMI2_E_INVALID_SENSOR;
    }

    return rslt;
}

/*!
 * @brief This internal API gets no-motion configurations like axes select,
 * duration, threshold and output-configuration.
 */
static int8_t get_no_motion_config(struct bmi2_no_motion_config *config, struct bmi2_dev *dev)
{
    /* Variable to define error */
    int8_t rslt;

    /* Array to define the feature configuration */
    uint8_t feat_config[BMI2_FEAT_SIZE_IN_BYTES] = { 0 };

    /* Variable to define the array offset */
    uint8_t idx = 0;

    /* Variable to define LSB */
    uint16_t lsb = 0;

    /* Variable to define MSB */
    uint16_t msb = 0;

    /* Variable to define a word */
    uint16_t lsb_msb = 0;

    /* Variable to set flag */
    uint8_t feat_found;

    /* Initialize feature configuration for no-motion */
    struct bmi2_feature_config no_mot_config = { 0, 0, 0 };

    /* Search for no-motion feature and extract its configuration details */
    feat_found = bmi2_extract_input_feat_config(&no_mot_config, BMI2_NO_MOTION, dev);
    if (feat_found)
    {
        /* Get the configuration from the page where no-motion feature resides */
        rslt = bmi2_get_feat_config(no_mot_config.page, feat_config, dev);
        if (rslt == BMI2_OK)
        {
            /* Define the offset for feature enable for no-motion */
            idx = no_mot_config.start_addr;

            /* Get word to calculate duration, x, y and z select */
            lsb = (uint16_t) feat_config[idx++];
            msb = ((uint16_t) feat_config[idx++] << 8);
            lsb_msb = lsb | msb;

            /* Get duration */
            config->duration = lsb_msb & BMI2_ANY_NO_MOT_DUR_MASK;

            /* Get x-select */
            config->select_x = (lsb_msb & BMI2_ANY_NO_MOT_X_SEL_MASK) >> BMI2_ANY_NO_MOT_X_SEL_POS;

            /* Get y-select */
            config->select_y = (lsb_msb & BMI2_ANY_NO_MOT_Y_SEL_MASK) >> BMI2_ANY_NO_MOT_Y_SEL_POS;

            /* Get z-select */
            config->select_z = (lsb_msb & BMI2_ANY_NO_MOT_Z_SEL_MASK) >> BMI2_ANY_NO_MOT_Z_SEL_POS;

            /* Get word to calculate threshold, output configuration from the same word */
            lsb = (uint16_t) feat_config[idx++];
            msb = ((uint16_t) feat_config[idx++] << 8);
            lsb_msb = lsb | msb;

            /* Get threshold */
            config->threshold = lsb_msb & BMI2_ANY_NO_MOT_THRES_MASK;
        }
    }
    else
    {
        rslt = BMI2_E_INVALID_SENSOR;
    }

    return rslt;
}

/*!
 * @brief This internal API is used to extract the output feature configuration
 * details from the look-up table.
 */
static uint8_t extract_output_feat_config(struct bmi2_feature_config *feat_output,
                                          uint8_t type,
                                          const struct bmi2_dev *dev)
{
    /* Variable to define loop */
    uint8_t loop = 0;

    /* Variable to set flag */
    uint8_t feat_found = BMI2_FALSE;

    /* Search for the output feature from the output configuration array */
    while (loop < dev->out_sens)
    {
        if (dev->feat_output[loop].type == type)
        {
            *feat_output = dev->feat_output[loop];
            feat_found = BMI2_TRUE;
            break;
        }

        loop++;
    }

    /* Return flag */
    return feat_found;
}

/*!
 * @brief This internal API gets the error status related to virtual frames.
 */
static int8_t get_vfrm_error_status(struct bmi2_vfrm_err_status *vfrm_err_stat, struct bmi2_dev *dev)
{
    /* Variable to define error */
    int8_t rslt;

    /* Array to define the feature configuration */
    uint8_t feat_config[BMI2_FEAT_SIZE_IN_BYTES] = { 0 };

    /* Variables to define index */
    uint8_t idx = 0;

    /* Variable to set flag */
    uint8_t feat_found;

    /* Initialize feature output for VFRM error status */
    struct bmi2_feature_config vfrm_err_cfg = { 0, 0, 0 };

    /* Search for VFRM error status feature and extract its configuration details */
    feat_found = extract_output_feat_config(&vfrm_err_cfg, BMI2_VFRM_STATUS, dev);
    if (feat_found)
    {
        /* Get the feature output configuration for VFRM error status */
        rslt = bmi2_get_feat_config(vfrm_err_cfg.page, feat_config, dev);
        if (rslt == BMI2_OK)
        {
            /* Define the offset in bytes for VFRM error status */
            idx = vfrm_err_cfg.start_addr;

            /* Increment index to get the error status */
            idx++;

            /* Internal error while acquiring lock for FIFO */
            vfrm_err_stat->lock_error = BMI2_GET_BITS(feat_config[idx], BMI2_VFRM_LOCK_ERR_STATUS);

            /* Internal error while writing byte into FIFO */
            vfrm_err_stat->write_error = BMI2_GET_BITS(feat_config[idx], BMI2_VFRM_WRITE_ERR_STATUS);

            /* Internal error while writing into FIFO */
            vfrm_err_stat->fatal_error = BMI2_GET_BITS(feat_config[idx], BMI2_VFRM_FATAL_ERR_STATUS);
        }
    }
    else
    {
        rslt = BMI2_E_INVALID_SENSOR;
    }

    return rslt;
}

/*!
 * @brief This internal API sets feature configuration to the sensor.
 */
static int8_t set_feat_config(const struct bmi2_sens_config *sens_cfg, uint8_t loop, struct bmi2_dev *dev)
{
    /* Variable to define error */
    int8_t rslt;

    switch (sens_cfg[loop].type)
    {
        /* Set dsd configuration */
        case BMI2_DOOR_STATE_DETECTOR:
            rslt = set_door_state_detector_config(&sens_cfg[loop].cfg.door_state_detector, dev);
            break;

        /* Set any motion configuration */
        case BMI2_ANY_MOTION:
            rslt = set_any_motion_config(&sens_cfg[loop].cfg.any_motion, dev);
            break;

        /* Set no motion configuration */
        case BMI2_NO_MOTION:
            rslt = set_no_motion_config(&sens_cfg[loop].cfg.no_motion, dev);
            break;

        default:
            rslt = BMI2_E_INVALID_SENSOR;
            break;
    }

    return rslt;
}

/*!
 * @brief This internal API gets feature configuration from the sensor.
 */
static int8_t get_feat_config(struct bmi2_sens_config *sens_cfg, uint8_t loop, struct bmi2_dev *dev)
{
    /* Variable to define error */
    int8_t rslt;

    switch (sens_cfg[loop].type)
    {
        /* Get dsd configuration */
        case BMI2_DOOR_STATE_DETECTOR:
            rslt = get_door_state_detector_config(&sens_cfg[loop].cfg.door_state_detector, dev);
            break;

        /* Get any motion configuration */
        case BMI2_ANY_MOTION:
            rslt = get_any_motion_config(&sens_cfg[loop].cfg.any_motion, dev);
            break;

        /* Get no motion configuration */
        case BMI2_NO_MOTION:
            rslt = get_no_motion_config(&sens_cfg[loop].cfg.no_motion, dev);
            break;

        default:
            rslt = BMI2_E_INVALID_SENSOR;
            break;
    }

    return rslt;
}

/*!
 * @brief This internal API is used to enable main sensors like accel, gyro, aux and temperature.
 */
static int8_t enable_main_sensors(uint64_t sensor_sel, struct bmi2_dev *dev)
{
    /* Variable to define error */
    int8_t rslt;

    /* Variable to store register values */
    uint8_t reg_data;

    rslt = bmi2_get_regs(BMI2_PWR_CTRL_ADDR, &reg_data, 1, dev);

    if (rslt == BMI2_OK)
    {
        /* Enable accelerometer */
        if (sensor_sel & BMI2_ACCEL_SENS_SEL)
        {
            reg_data = BMI2_SET_BITS(reg_data, BMI2_ACC_EN, BMI2_ENABLE);
        }

        /* Enable gyroscope */
        if (sensor_sel & BMI2_GYRO_SENS_SEL)
        {
            reg_data = BMI2_SET_BITS(reg_data, BMI2_GYR_EN, BMI2_ENABLE);
        }

        /* Enable auxiliary sensor */
        if (sensor_sel & BMI2_AUX_SENS_SEL)
        {
            reg_data = BMI2_SET_BIT_POS0(reg_data, BMI2_AUX_EN, BMI2_ENABLE);
        }

        /* Enable temperature sensor */
        if (sensor_sel & BMI2_TEMP_SENS_SEL)
        {
            reg_data = BMI2_SET_BITS(reg_data, BMI2_TEMP_EN, BMI2_ENABLE);
        }

        /* Enable the sensors that are set in the power control register */
        if (sensor_sel & BMI2_MAIN_SENSORS)
        {
            rslt = bmi2_set_regs(BMI2_PWR_CTRL_ADDR, &reg_data, 1, dev);
        }
    }

    return rslt;
}

/*!
 * @brief This internal API is used to enable sensor features.
 */
static int8_t enable_sensor_features(uint64_t sensor_sel, struct bmi2_dev *dev)
{
    /* Variable to define error */
    int8_t rslt = BMI2_OK;

    /* Enable gyroscope user gain */
    if (sensor_sel & BMI2_GYRO_GAIN_UPDATE_SEL)
    {
        rslt = set_gyro_user_gain(BMI2_ENABLE, dev);
    }

    /* Enable high-g feature */
    if (sensor_sel & BMI2_DOOR_STATE_DETECTOR_SEL)
    {
        rslt = set_door_state_detector(BMI2_ENABLE, dev);
    }

    /* Enable gyroscope self-offset correction feature */
    if (sensor_sel & BMI2_GYRO_SELF_OFF_SEL)
    {
        rslt = set_gyro_self_offset_corr(BMI2_ENABLE, dev);
    }

    /* Enable any motion feature */
    if (sensor_sel & BMI2_ANY_MOT_SEL)
    {
        rslt = set_any_motion(BMI2_ENABLE, dev);
    }

    /* Enable no motion feature */
    if (sensor_sel & BMI2_NO_MOT_SEL)
    {
        rslt = set_no_motion(BMI2_ENABLE, dev);
    }

    return rslt;
}

/*!
 * @brief This internal API is used to disable main sensors like accel, gyro, aux and temperature.
 */
static int8_t disable_main_sensors(uint64_t sensor_sel, struct bmi2_dev *dev)
{
    /* Variable to define error */
    int8_t rslt;

    /* Variable to store register values */
    uint8_t reg_data;

    rslt = bmi2_get_regs(BMI2_PWR_CTRL_ADDR, &reg_data, 1, dev);

    if (rslt == BMI2_OK)
    {
        /* Disable accelerometer */
        if (sensor_sel & BMI2_ACCEL_SENS_SEL)
        {
            reg_data = BMI2_SET_BIT_VAL0(reg_data, BMI2_ACC_EN);
        }

        /* Disable gyroscope */
        if (sensor_sel & BMI2_GYRO_SENS_SEL)
        {
            reg_data = BMI2_SET_BIT_VAL0(reg_data, BMI2_GYR_EN);
        }

        /* Disable auxiliary sensor */
        if (sensor_sel & BMI2_AUX_SENS_SEL)
        {
            reg_data = BMI2_SET_BIT_VAL0(reg_data, BMI2_AUX_EN);
        }

        /* Disable temperature sensor */
        if (sensor_sel & BMI2_TEMP_SENS_SEL)
        {
            reg_data = BMI2_SET_BIT_VAL0(reg_data, BMI2_TEMP_EN);
        }

        /* Disable the sensors that are set in the power control register */
        if (sensor_sel & BMI2_MAIN_SENSORS)
        {
            rslt = bmi2_set_regs(BMI2_PWR_CTRL_ADDR, &reg_data, 1, dev);
        }
    }

    return rslt;
}

/*!
 * @brief This internal API is used to disable sensor features.
 */
static int8_t disable_sensor_features(uint64_t sensor_sel, struct bmi2_dev *dev)
{
    /* Variable to define error */
    int8_t rslt = BMI2_OK;

    /* Disable gyroscope user gain */
    if (sensor_sel & BMI2_GYRO_GAIN_UPDATE_SEL)
    {
        rslt = set_gyro_user_gain(BMI2_DISABLE, dev);
    }

    /* Disable dsd feature */
    if (sensor_sel & BMI2_DOOR_STATE_DETECTOR_SEL)
    {
        rslt = set_door_state_detector(BMI2_DISABLE, dev);
    }

    /* Disable gyroscope self-offset correction feature */
    if (sensor_sel & BMI2_GYRO_SELF_OFF_SEL)
    {
        rslt = set_gyro_self_offset_corr(BMI2_DISABLE, dev);
    }

    /* Disable any-motion feature */
    if (sensor_sel & BMI2_ANY_MOT_SEL)
    {
        rslt = set_any_motion(BMI2_DISABLE, dev);
    }

    /* Disable no-motion feature */
    if (sensor_sel & BMI2_NO_MOT_SEL)
    {
        rslt = set_no_motion(BMI2_DISABLE, dev);
    }

    return rslt;
}

/*!
 * @brief This internal API sets door state detector configurations like init remap setting.
 */
static int8_t set_door_state_detector_config_primary(const struct bmi2_door_state_detector_config *config,
                                                     struct bmi2_dev *dev)
{
    return set_door_state_detector_config_common(config, dev, BMI2_DOOR_STATE_DETECTOR);
}

/*!
 * @brief This internal API sets door closed threshold.
 */
static int8_t set_door_closed_thr(const struct bmi2_door_state_detector_config *config, struct bmi2_dev *dev)
{
    return set_door_state_detector_config_common(config, dev, BMI2_DOOR_STATE_DETECTOR_2);
}

/*!
 * @brief This internal API sets door state detector configurations.
 */
static int8_t set_door_state_detector_config_common(const struct bmi2_door_state_detector_config *config,
                                                    struct bmi2_dev *dev,
                                                    uint8_t feature_type)
{
    /* Variable to define error */
    int8_t rslt;

    /* Array to define the feature configuration */
    uint8_t feat_config[BMI2_FEAT_SIZE_IN_BYTES] = { 0 };

    /* Variable to define the array offset */
    uint8_t idx = 0;

    /* Variable to define count */
    uint8_t index = 0;

    /* Variable to set flag */
    uint8_t feat_found;

    /* Initialize feature configuration for door state detector */
    struct bmi2_feature_config dsd_config = { 0, 0, 0 };

    /* Copy the feature configuration address to a local pointer */
    uint16_t *data_p = (uint16_t *) (void *)feat_config;

    /* Search for door state detector feature and extract its configuration details */
    feat_found = bmi2_extract_input_feat_config(&dsd_config, feature_type, dev);
    if (feat_found)
    {
        /* Get the configuration from the page where door state detector feature resides */
        rslt = bmi2_get_feat_config(dsd_config.page, feat_config, dev);
        if (rslt == BMI2_OK)
        {
            /* Define the offset in bytes for door state detector select */
            idx = dsd_config.start_addr;

            /* Get offset in words since all the features are set in words length */
            idx = idx / 2;

            if (feature_type == BMI2_DOOR_STATE_DETECTOR)
            {
                /* Set remap parameter */
                *(data_p + idx) = BMI2_SET_BITS(*(data_p + idx), BMI270_DSD_REMAP_FLAG, config->remap_flag);

                *(data_p + idx) = BMI2_SET_BITS(*(data_p + idx), BMI270_DSD_Z_SIGN, config->z_sign);

                *(data_p + idx) = BMI2_SET_BITS(*(data_p + idx), BMI270_DSD_Z_AXIS, config->z_axis);

                *(data_p + idx) = BMI2_SET_BITS(*(data_p + idx), BMI270_DSD_GYRO_CALIB_APPLY, config->gyro_calib_apply);

                /* Increment offset by 1 word to set threshold and output configuration */
                idx++;

                /* Set gyro calibration */
                *(data_p + idx) = BMI2_SET_BIT_POS0(*(data_p + idx), BMI270_DSD_INIT_CALIB_THR, config->init_calib_thr);

                *(data_p +
                  idx) = BMI2_SET_BITS(*(data_p + idx), BMI270_DSD_RESET_ENABLE_FLAG, config->reset_enable_flag);

                /* Increment offset by 1 more word to get the total length in words */
                idx++;

                /* Set bias x low word */
                *(data_p +
                  idx) = BMI2_SET_BIT_POS0(*(data_p + idx), BMI270_DSD_BIAS_X_LOW_WORD, config->bias_x_low_word);

                /* Increment offset by 1 more word to get the total length in words */
                idx++;

                /* Set bias x high word */
                *(data_p + idx) = BMI2_SET_BIT_POS0(*(data_p + idx),
                                                    BMI270_DSD_BIAS_X_HIGH_WORD,
                                                    config->bias_x_high_word);

                /* Increment offset by 1 more word to get the total length in words */
                idx++;

                /* Set bias y low word */
                *(data_p +
                  idx) = BMI2_SET_BIT_POS0(*(data_p + idx), BMI270_DSD_BIAS_Y_LOW_WORD, config->bias_y_low_word);

                /* Increment offset by 1 more word to get the total length in words */
                idx++;

                /* Set bias y high word */
                *(data_p + idx) = BMI2_SET_BIT_POS0(*(data_p + idx),
                                                    BMI270_DSD_BIAS_Y_HIGH_WORD,
                                                    config->bias_y_high_word);

                /* Increment offset by 1 more word to get the total length in words */
                idx++;

                /* Set bias z low word */
                *(data_p +
                  idx) = BMI2_SET_BIT_POS0(*(data_p + idx), BMI270_DSD_BIAS_Z_LOW_WORD, config->bias_z_low_word);

                /* Increment offset by 1 more word to get the total length in words */
                idx++;

                /* Set bias z high word */
                *(data_p + idx) = BMI2_SET_BIT_POS0(*(data_p + idx),
                                                    BMI270_DSD_BIAS_Z_HIGH_WORD,
                                                    config->bias_z_high_word);

                /* Increment offset by 1 more word to get the total length in words */
                idx++;
            }
            else if (feature_type == BMI2_DOOR_STATE_DETECTOR_2)
            {
                /* Set door closed threshold parameter */
                *(data_p +
                  idx) = BMI2_SET_BIT_POS0(*(data_p + idx), BMI270_DSD_DOOR_CLOSED_THR, config->door_closed_thr);

                /* Increment offset by 1 more word to get the total length in words */
                idx++;
            }

            /* Get total length in bytes to copy from local pointer to the array */
            idx = (uint8_t)(idx * 2) - dsd_config.start_addr;

            /* Copy the bytes to be set back to the array */
            for (index = 0; index < idx; index++)
            {
                feat_config[dsd_config.start_addr + index] = *((uint8_t *) data_p + dsd_config.start_addr + index);
            }

            /* Set the configuration back to the page */
            rslt = bmi2_set_regs(BMI2_FEATURES_REG_ADDR, feat_config, BMI2_FEAT_SIZE_IN_BYTES, dev);
        }
    }
    else
    {
        rslt = BMI2_E_INVALID_SENSOR;
    }

    return rslt;
}

/*!
 * @brief This internal API gets door state detector configurations like init remap setting.
 */
static int8_t get_door_state_detector_config_primary(struct bmi2_door_state_detector_config *config,
                                                     struct bmi2_dev *dev)
{
    return get_door_state_detector_config_common(config, dev, BMI2_DOOR_STATE_DETECTOR);
}

/*!
 * @brief This internal API gets door closed threshold.
 */
static int8_t get_door_closed_thr(struct bmi2_door_state_detector_config *config, struct bmi2_dev *dev)
{
    return get_door_state_detector_config_common(config, dev, BMI2_DOOR_STATE_DETECTOR_2);
}

/*!
 * @brief This internal API gets door state detector configurations.
 */
static int8_t get_door_state_detector_config_common(struct bmi2_door_state_detector_config *config,
                                                    struct bmi2_dev *dev,
                                                    uint8_t feature_type)
{
    /* Variable to define error */
    int8_t rslt;

    /* Array to define the feature configuration */
    uint8_t feat_config[BMI2_FEAT_SIZE_IN_BYTES] = { 0 };

    /* Variable to define the array offset */
    uint8_t idx = 0;

    /* Variable to define LSB */
    uint16_t lsb;

    /* Variable to define MSB */
    uint16_t msb;

    /* Variable to define a word */
    uint16_t lsb_msb;

    /* Variable to set flag */
    uint8_t feat_found;

    /* Initialize feature configuration for door state detector */
    struct bmi2_feature_config dsd_config = { 0, 0, 0 };

    /* Search for door state detector feature and extract its configuration details */
    feat_found = bmi2_extract_input_feat_config(&dsd_config, feature_type, dev);
    if (feat_found)
    {
        /* Get the configuration from the page where door state detector feature resides */
        rslt = bmi2_get_feat_config(dsd_config.page, feat_config, dev);

        if (rslt == BMI2_OK)
        {
            /* Define the offset for feature enable for door state detector */
            idx = dsd_config.start_addr;

            if (feature_type == BMI2_DOOR_STATE_DETECTOR)
            {
                /* Get word to calculate remap_flag, z_axis, z_sign configuration from the same word */
                lsb = (uint16_t)feat_config[idx++];
                msb = ((uint16_t)feat_config[idx++] << 8);
                lsb_msb = lsb | msb;

                config->dsd_enable = BMI2_GET_BIT_POS0(lsb_msb, BMI270_DSD_ENABLE);

                config->remap_flag = BMI2_GET_BITS(lsb_msb, BMI270_DSD_REMAP_FLAG);

                config->z_sign = BMI2_GET_BITS(lsb_msb, BMI270_DSD_Z_SIGN);

                config->z_axis = BMI2_GET_BITS(lsb_msb, BMI270_DSD_Z_AXIS);

                config->gyro_calib_apply = BMI2_GET_BITS(lsb_msb, BMI270_DSD_GYRO_CALIB_APPLY);

                /* Get word to calculate init_calib_thr, reset_enable_flag configuration from the same word */
                lsb = (uint16_t)feat_config[idx++];
                msb = ((uint16_t)feat_config[idx++] << 8);
                lsb_msb = lsb | msb;

                config->init_calib_thr = BMI2_GET_BIT_POS0(lsb_msb, BMI270_DSD_INIT_CALIB_THR);

                config->reset_enable_flag = BMI2_GET_BITS(lsb_msb, BMI270_DSD_RESET_ENABLE_FLAG);

                /* Get word to calculate bias_x_low_word configuration from the same word */
                lsb = (uint16_t)feat_config[idx++];
                msb = ((uint16_t)feat_config[idx++] << 8);

                config->bias_x_low_word = lsb | msb;

                /* Get word to calculate bias_x_high_word configuration from the same word */
                lsb = (uint16_t)feat_config[idx++];
                msb = ((uint16_t)feat_config[idx++] << 8);

                config->bias_x_high_word = lsb | msb;

                /* Get word to calculate bias_y_low_word configuration from the same word */
                lsb = (uint16_t)feat_config[idx++];
                msb = ((uint16_t)feat_config[idx++] << 8);

                config->bias_y_low_word = lsb | msb;

                /* Get word to calculate bias_y_high_word configuration from the same word */
                lsb = (uint16_t)feat_config[idx++];
                msb = ((uint16_t)feat_config[idx++] << 8);

                config->bias_y_high_word = lsb | msb;

                /* Get word to calculate bias_z_low_word configuration from the same word */
                lsb = (uint16_t)feat_config[idx++];
                msb = ((uint16_t)feat_config[idx++] << 8);

                config->bias_z_low_word = lsb | msb;

                /* Get word to calculate bias_z_low_word configuration from the same word */
                lsb = (uint16_t)feat_config[idx++];
                msb = ((uint16_t)feat_config[idx++] << 8);

                config->bias_z_high_word = lsb | msb;
            }
            else if (feature_type == BMI2_DOOR_STATE_DETECTOR_2)
            {
                /* Get word to calculate door closed threshold configuration from the same word */
                lsb = (uint16_t)feat_config[idx++];
                msb = ((uint16_t)feat_config[idx++] << 8);
                lsb_msb = lsb | msb;
                config->door_closed_thr = BMI2_GET_BIT_POS0(lsb_msb, BMI270_DSD_DOOR_CLOSED_THR);
            }
        }
    }
    else
    {
        rslt = BMI2_E_INVALID_SENSOR;
    }

    return rslt;
}

/*!
 * @brief This internal API gets door state detector configurations like init remap setting.
 */
static int8_t get_door_state_detector_config(struct bmi2_door_state_detector_config *config, struct bmi2_dev *dev)
{
    /* Variable to define error */
    int8_t rslt;

    /* Get primary configuration */
    rslt = get_door_state_detector_config_primary(config, dev);
    if (rslt == BMI2_OK)
    {
        /* Get door closed threshold */
        rslt = get_door_closed_thr(config, dev);
    }

    return rslt;
}

/*!
 * @brief This internal API sets door state detector configurations like init remap setting.
 */
static int8_t set_door_state_detector_config(const struct bmi2_door_state_detector_config *config, struct bmi2_dev *dev)
{
    /* Variable to define error */
    int8_t rslt;

    /* Set primary configuration */
    rslt = set_door_state_detector_config_primary(config, dev);
    if (rslt == BMI2_OK)
    {
        /* Set door closed threshold */
        rslt = set_door_closed_thr(config, dev);
    }

    return rslt;
}
